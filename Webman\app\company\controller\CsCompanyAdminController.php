<?php

namespace app\company\controller;

use app\controller\CompanyBaseController;
use app\model\CsCompanyAdmin;
use support\Request;
use support\Response;

class CsCompanyAdminController extends CompanyBaseController
{
    // 验证器
    protected $validateName = 'CsCompanyAdminValidate';

    // 当前主模型
    protected $modelName = 'CsCompanyAdmin';


    public function editSelf(Request $request): Response
    {
        $admin = CsCompanyAdmin::find($request->admin_id);
        $backData = [
            'id' => $admin->id,
            'username' => $admin->username,
            'nickname' => $admin->nickname,
            'password' => $admin->password,
            'avatar' => $admin->avatar,
            'avatar_url' => get_image_url($admin->avatar),
        ];
        return success($backData);
    }

    public function editSelfPost(Request $request): Response
    {
        $admin = CsCompanyAdmin::find($request->admin_id);
        $admin->nickname = $request->post('nickname','');
        $admin->password = $request->post('password','');
        $admin->avatar = $request->post('avatar','');
        $admin->save();
        return success('');
    }

}
