/**
 * 忘记密码功能测试文件
 * 用于验证忘记密码相关API是否正常工作
 */

import { 
  sendForgotPasswordSms,
  verifyForgotPasswordSms,
  resetPassword,
  resendForgotPasswordSms
} from '@/services/authApi'

// 测试发送忘记密码验证码
export const testSendForgotPasswordSms = async (phone: string) => {
  try {
    console.log('测试发送忘记密码验证码...')
    console.log('手机号:', phone)
    
    const result = await sendForgotPasswordSms({ phone })
    console.log('发送验证码成功:', result)
    return true
  } catch (error) {
    console.error('发送验证码失败:', error)
    return false
  }
}

// 测试验证忘记密码验证码
export const testVerifyForgotPasswordSms = async (phone: string, smsCode: string) => {
  try {
    console.log('测试验证忘记密码验证码...')
    console.log('手机号:', phone)
    console.log('验证码:', smsCode)
    
    const result = await verifyForgotPasswordSms({ phone, sms_code: smsCode })
    console.log('验证码验证成功:', result)
    return result.token
  } catch (error) {
    console.error('验证码验证失败:', error)
    return null
  }
}

// 测试重置密码
export const testResetPassword = async (phone: string, token: string, newPassword: string) => {
  try {
    console.log('测试重置密码...')
    console.log('手机号:', phone)
    console.log('Token:', token)
    
    const result = await resetPassword({
      phone,
      token,
      new_password: newPassword,
      confirm_password: newPassword
    })
    console.log('重置密码成功:', result)
    return true
  } catch (error) {
    console.error('重置密码失败:', error)
    return false
  }
}

// 测试重新发送验证码
export const testResendForgotPasswordSms = async (phone: string) => {
  try {
    console.log('测试重新发送验证码...')
    console.log('手机号:', phone)
    
    const result = await resendForgotPasswordSms({ phone })
    console.log('重新发送验证码成功:', result)
    return true
  } catch (error) {
    console.error('重新发送验证码失败:', error)
    return false
  }
}

// 运行完整的忘记密码流程测试
export const runForgotPasswordFlowTest = async (phone: string, smsCode: string, newPassword: string) => {
  console.log('=== 开始忘记密码流程测试 ===')
  
  // 步骤1: 发送验证码
  const step1Success = await testSendForgotPasswordSms(phone)
  if (!step1Success) {
    console.log('❌ 步骤1失败：发送验证码')
    return false
  }
  console.log('✅ 步骤1成功：发送验证码')
  
  // 步骤2: 验证验证码
  const token = await testVerifyForgotPasswordSms(phone, smsCode)
  if (!token) {
    console.log('❌ 步骤2失败：验证验证码')
    return false
  }
  console.log('✅ 步骤2成功：验证验证码')
  
  // 步骤3: 重置密码
  const step3Success = await testResetPassword(phone, token, newPassword)
  if (!step3Success) {
    console.log('❌ 步骤3失败：重置密码')
    return false
  }
  console.log('✅ 步骤3成功：重置密码')
  
  console.log('=== 忘记密码流程测试完成 ===')
  console.log('🎉 所有步骤都成功完成！')
  return true
}

// 在浏览器控制台中使用的便捷方法
if (typeof window !== 'undefined') {
  (window as any).testForgotPassword = {
    sendSms: testSendForgotPasswordSms,
    verifySms: testVerifyForgotPasswordSms,
    resetPassword: testResetPassword,
    resendSms: testResendForgotPasswordSms,
    runFullTest: runForgotPasswordFlowTest
  }
  
  console.log('忘记密码测试工具已加载到 window.testForgotPassword')
  console.log('使用方法：')
  console.log('- window.testForgotPassword.sendSms("13800138000") - 测试发送验证码')
  console.log('- window.testForgotPassword.verifySms("13800138000", "123456") - 测试验证验证码')
  console.log('- window.testForgotPassword.resetPassword("13800138000", "token", "newpass") - 测试重置密码')
  console.log('- window.testForgotPassword.resendSms("13800138000") - 测试重新发送验证码')
  console.log('- window.testForgotPassword.runFullTest("13800138000", "123456", "newpass") - 运行完整流程测试')
}