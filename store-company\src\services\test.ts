/**
 * API 工具测试文件
 * 用于验证 API 请求工具是否正常工作
 */

import { service, getAdminList } from '@/services'
import { login } from '@/services/authApi'

// 测试基础 axios 实例
export const testAxiosInstance = async () => {
  try {
    console.log('测试 axios 实例配置...')
    console.log('Base URL:', service.defaults.baseURL)
    console.log('Timeout:', service.defaults.timeout)
    
    // 测试请求拦截器是否正常添加 headers
    const config = service.defaults
    console.log('默认配置:', config)
    
    return true
  } catch (error) {
    console.error('axios 实例测试失败:', error)
    return false
  }
}

// 测试 API Key 生成
export const testApiKeyGeneration = () => {
  try {
    console.log('测试 API Key 生成...')
    
    // 模拟请求拦截器中的逻辑
    const timestamp = Math.floor(Date.now() / 1000)
    console.log('当前时间戳:', timestamp)
    
    // 注意：这里无法直接访问 generateApiKey 函数，因为它是私有的
    // 但可以通过发送实际请求来验证
    
    return true
  } catch (error) {
    console.error('API Key 生成测试失败:', error)
    return false
  }
}

// 测试登录 API（需要后端服务运行）
export const testLoginApi = async () => {
  try {
    console.log('测试登录 API...')
    
    // 注意：这需要后端服务正在运行
    const result = await login({
      username: 'test',
      password: 'test'
    })
    
    console.log('登录测试结果:', result)
    return true
  } catch (error) {
    console.error('登录 API 测试失败:', error)
    console.log('这是正常的，因为后端服务可能未运行或测试账号不存在')
    return false
  }
}

// 测试管理员列表 API（需要后端服务运行）
export const testAdminListApi = async () => {
  try {
    console.log('测试管理员列表 API...')
    
    const result = await getAdminList({
      page: 1,
      page_size: 10
    })
    
    console.log('管理员列表测试结果:', result)
    return true
  } catch (error) {
    console.error('管理员列表 API 测试失败:', error)
    console.log('这是正常的，因为后端服务可能未运行或需要登录')
    return false
  }
}

// 运行所有测试
export const runAllTests = async () => {
  console.log('=== 开始 API 工具测试 ===')
  
  const results = {
    axiosInstance: await testAxiosInstance(),
    apiKeyGeneration: testApiKeyGeneration(),
    loginApi: await testLoginApi(),
    adminListApi: await testAdminListApi()
  }
  
  console.log('=== 测试结果汇总 ===')
  console.log('axios 实例测试:', results.axiosInstance ? '✅ 通过' : '❌ 失败')
  console.log('API Key 生成测试:', results.apiKeyGeneration ? '✅ 通过' : '❌ 失败')
  console.log('登录 API 测试:', results.loginApi ? '✅ 通过' : '❌ 失败（需要后端服务）')
  console.log('管理员列表 API 测试:', results.adminListApi ? '✅ 通过' : '❌ 失败（需要后端服务）')
  
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  
  console.log(`总体结果: ${passedTests}/${totalTests} 测试通过`)
  
  return results
}

// 在浏览器控制台中使用的便捷方法
if (typeof window !== 'undefined') {
  (window as any).testApi = {
    runAllTests,
    testAxiosInstance,
    testApiKeyGeneration,
    testLoginApi,
    testAdminListApi
  }
  
  console.log('API 测试工具已加载到 window.testApi')
  console.log('使用方法：')
  console.log('- window.testApi.runAllTests() - 运行所有测试')
  console.log('- window.testApi.testAxiosInstance() - 测试 axios 实例')
  console.log('- window.testApi.testLoginApi() - 测试登录 API')
}
