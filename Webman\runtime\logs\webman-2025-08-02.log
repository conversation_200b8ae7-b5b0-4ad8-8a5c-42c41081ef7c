[2025-08-02 07:31:27] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [42.6681ms] [webman/log]
 [] []
[2025-08-02 07:31:27] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [42.3469ms] [webman/log]
[Redis]	[connection:default] ...
 [] []
[2025-08-02 07:48:25] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/logout [0.06508ms] [webman/log]
 [] []
[2025-08-02 07:48:25] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/logout [0.39410ms] [webman/log]
[POST]	array (
)
[Redis]	[connection:default] Redis::exists('company:11657bf91cf2dbf4e398e88e861411e1') (0.19 ms)
 [] []
[2025-08-02 08:31:23] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.06103ms] [webman/log]
 [] []
[2025-08-02 08:31:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.72216ms] [webman/log]
 [] []
[2025-08-02 10:48:02] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [26.1919ms] [webman/log]
 [] []
[2025-08-02 10:48:02] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [4.20904ms] [webman/log]
 [] []
[2025-08-02 11:23:15] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.07200ms] [webman/log]
 [] []
[2025-08-02 11:23:15] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.20003ms] [webman/log]
 [] []
[2025-08-02 11:23:15] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.16593ms] [webman/log]
 [] []
[2025-08-02 11:24:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.20790ms] [webman/log]
 [] []
[2025-08-02 11:24:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.16403ms] [webman/log]
 [] []
[2025-08-02 11:24:05] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.03600ms] [webman/log]
 [] []
[2025-08-02 11:24:05] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.17404ms] [webman/log]
 [] []
[2025-08-02 11:24:17] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.03504ms] [webman/log]
 [] []
[2025-08-02 11:24:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.30779ms] [webman/log]
 [] []
[2025-08-02 11:24:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/login [0.05197ms] [webman/log]
 [] []
[2025-08-02 11:24:35] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [88.3669ms] [webman/log]
[POST]	array (
  'username' => '19815092140',
  'password' => '96e79218965eb72c92a549dd5a330112',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '19815092140') and `cs_company_admins`.`deleted_at` is null limit 1 [34.27 ms]
 [] []
[2025-08-02 11:24:46] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [130.264ms] [webman/log]
[POST]	array (
  'username' => '19815092140',
  'password' => 'e3ceb5881a0a1fdaad01296d7554868d',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '19815092140') and `cs_company_admins`.`deleted_at` is null limit 1 [0.79 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` = 13 and `cs_companys`.`deleted_at` is null limit 1 [7.21 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `login_time` = '2025-08-02 11:24:46', `cs_company_admins`.`updated_at` = '2025-08-02 11:24:46' where `id` = 13 [16.14 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 1 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [5.95 ms]
[SQL]	[connection:mysql] select `id` from `cs_menus` where (`menu_type` = 1 and `status` = 1) and `cs_menus`.`deleted_at` is null [8.08 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `menu_img`, `menu_sign`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and 0 = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.78 ms]
[Redis]	[connection:default] ...
 [] []
[2025-08-02 11:28:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.26082ms] [webman/log]
 [] []
[2025-08-02 11:28:48] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18906ms] [webman/log]
 [] []
[2025-08-02 11:29:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [27.6257ms] [webman/log]
 [] []
[2025-08-02 11:29:31] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.22602ms] [webman/log]
 [] []
[2025-08-02 11:29:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.40292ms] [webman/log]
 [] []
[2025-08-02 11:29:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.19216ms] [webman/log]
 [] []
[2025-08-02 11:30:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.26607ms] [webman/log]
 [] []
[2025-08-02 11:30:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.24819ms] [webman/log]
 [] []
[2025-08-02 11:30:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18501ms] [webman/log]
 [] []
[2025-08-02 11:30:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.21815ms] [webman/log]
 [] []
[2025-08-02 12:21:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getUserInfo [0.04720ms] [webman/log]
 [] []
[2025-08-02 12:21:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getUserInfo [78.0420ms] [webman/log]
[SQL]	[connection:mysql] select `expired_at` from `cs_companys` where (`id` = '13') and `cs_companys`.`deleted_at` is null limit 1 [14.93 ms]
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '13' and `cs_company_admins`.`deleted_at` is null limit 1 [1.28 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` in (13) and `cs_companys`.`deleted_at` is null [0.86 ms]
[SQL]	[connection:mysql] select * from `cs_product_versions` where `cs_product_versions`.`id` in (1) and `cs_product_versions`.`deleted_at` is null [8.48 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.77 ms]
[Redis]	[connection:default] ...
 [] []
[2025-08-02 12:23:49] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [0.06723ms] [webman/log]
 [] []
[2025-08-02 12:23:49] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [52.3540ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:6bb8d51482345ecbae91470b16b041e5') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:6bb8d51482345ecbae91470b16b041e5') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:13') (0.11 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '13' and `cs_shops`.`deleted_at` is null [7.71 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '13' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.88 ms]
 [] []
[2025-08-02 12:23:53] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/logout [0.04100ms] [webman/log]
 [] []
[2025-08-02 12:23:53] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/logout [1.01494ms] [webman/log]
[POST]	array (
)
[Redis]	[connection:default] Redis::exists('company:6bb8d51482345ecbae91470b16b041e5') (0.28 ms)
[Redis]	[connection:default] Redis::get('company:6bb8d51482345ecbae91470b16b041e5') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:13') (0.1 ms)
[Redis]	[connection:default] Redis::del('company:6bb8d51482345ecbae91470b16b041e5') (0.11 ms)
 [] []
[2025-08-02 12:24:00] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [109.282ms] [webman/log]
[POST]	array (
  'username' => '***********',
  'password' => '96e79218965eb72c92a549dd5a330112',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '***********') and `cs_company_admins`.`deleted_at` is null limit 1 [1.29 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` = 18 and `cs_companys`.`deleted_at` is null limit 1 [0.93 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `login_time` = '2025-08-02 12:23:59', `cs_company_admins`.`updated_at` = '2025-08-02 12:23:59' where `id` = 18 [97.04 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 1 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [1.35 ms]
[Redis]	[connection:default] Redis::setex('company:574874ffd8b53a149d05548ea506cfb9', '2592000', '18-1-18') (0.27 ms)
[SQL]	[connection:mysql] select `id` from `cs_menus` where (`menu_type` = 1 and `status` = 1) and `cs_menus`.`deleted_at` is null [1.4 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `menu_img`, `menu_sign`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and 0 = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.73 ms]
 [] []
[2025-08-02 12:24:02] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getUserInfo [5.83600ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.26 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `expired_at` from `cs_companys` where (`id` = '18') and `cs_companys`.`deleted_at` is null limit 1 [0.67 ms]
[Redis]	[connection:default] Redis::setex('company_expired_at:18', '86400', '2025-08-11') (0.16 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '18' and `cs_company_admins`.`deleted_at` is null limit 1 [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` in (18) and `cs_companys`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select * from `cs_product_versions` where `cs_product_versions`.`id` in (1) and `cs_product_versions`.`deleted_at` is null [0.86 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.47 ms]
 [] []
[2025-08-02 12:29:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.25606ms] [webman/log]
 [] []
[2025-08-02 12:29:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.16522ms] [webman/log]
 [] []
[2025-08-02 12:32:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getUserInfo [4.73499ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '18' and `cs_company_admins`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` in (18) and `cs_companys`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select * from `cs_product_versions` where `cs_product_versions`.`id` in (1) and `cs_product_versions`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.48 ms]
 [] []
[2025-08-02 12:44:09] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.20599ms] [webman/log]
 [] []
[2025-08-02 12:47:14] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18811ms] [webman/log]
 [] []
[2025-08-02 12:51:06] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getUserInfo [4.72712ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '18' and `cs_company_admins`.`deleted_at` is null limit 1 [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` in (18) and `cs_companys`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select * from `cs_product_versions` where `cs_product_versions`.`id` in (1) and `cs_product_versions`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.48 ms]
 [] []
[2025-08-02 12:52:44] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [2.91705ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.85 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.79 ms]
 [] []
[2025-08-02 12:54:40] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [0.04005ms] [webman/log]
 [] []
[2025-08-02 12:54:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [2.91204ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.23 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.75 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.6 ms]
 [] []
[2025-08-02 12:54:45] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [0.04100ms] [webman/log]
 [] []
[2025-08-02 12:54:45] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [2.69508ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.69 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.58 ms]
 [] []
[2025-08-02 12:54:47] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [0.04005ms] [webman/log]
 [] []
[2025-08-02 12:54:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.24606ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.4 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.87 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.63 ms]
 [] []
[2025-08-02 12:57:04] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/addPost [0.06008ms] [webman/log]
 [] []
[2025-08-02 12:57:05] default.ERROR: 商品441库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品442库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品443库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品444库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品445库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品446库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品447库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品448库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品449库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品450库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品451库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品452库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品453库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品454库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品455库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品456库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品457库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品458库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品459库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品460库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品461库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品462库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品463库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品464库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品465库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品466库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 商品467库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品292库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品293库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品294库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品295库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品296库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品297库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品298库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品299库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品300库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品301库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品302库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品303库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品304库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品305库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品306库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品307库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品308库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 配品309库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 套餐98库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 套餐99库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 套餐100库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 套餐101库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 套餐102库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.ERROR: 套餐103库存初始化失败: Exception::__construct(): Passing null to parameter #1 ($message) of type string is deprecated [] []
[2025-08-02 12:57:05] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsShop/addPost [548.534ms] [webman/log]
[POST]	array (
  'shop_name' => '松间茗坊',
  'nickname' => '张建文',
  'mobile' => '***********',
  'password' => '111111',
  'address' => '江西省九江市浔阳西路38号',
  'service_phone' => '0791-1234567',
  'status' => 1,
  'remark' => '化用 “明月松间照”，松的苍劲与茶的温润相衬，自带山林清气',
  'business_hours' => 
  array (
    'monday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'tuesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'wednesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'thursday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'friday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'saturday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'sunday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` = '18' and `cs_companys`.`deleted_at` is null limit 1 [0.59 ms]
[SQL]	[connection:mysql] select `id`, (select count(*) from `cs_shops` where `cs_companys`.`id` = `cs_shops`.`company_id` and `cs_shops`.`deleted_at` is null) as `cs_shop_count` from `cs_companys` where `cs_companys`.`id` in (18) [0.56 ms]
[SQL]	[connection:mysql] insert into `cs_shops` (`shop_name`, `nickname`, `mobile`, `password`, `address`, `service_phone`, `status`, `remark`, `business_hours`, `company_id`, `updated_at`, `created_at`) values ('松间茗坊', '张建文', '***********', '111111', '江西省九江市浔阳西路38号', '0791-1234567', 1, '化用 “明月松间照”，松的苍劲与茶的温润相衬，自带山林清气', '"{\"monday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"},\"tuesday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"},\"wednesday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"},\"thursday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"},\"friday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"},\"saturday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"},\"sunday\":{\"is_open\":true,\"open\":\"09:00\",\"close\":\"22:00\"}}"', '18', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [1.1 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where (`username` = '***********') and `cs_shop_admins`.`deleted_at` is null limit 1 [6.76 ms]
[SQL]	[connection:mysql] insert into `cs_shop_admins` (`nickname`, `username`, `password`, `status`, `shop_id`, `role_id`, `is_origin`, `updated_at`, `created_at`) values ('***********', '***********', '111111', 1, 28, 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_user_levels` (`user_level_name`, `goods_discount`, `room_discount`, `service_discount`, `shop_id`, `can_not_delete`, `updated_at`, `created_at`) values ('普通会员', 10, 10, 10, 28, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [8.33 ms]
[SQL]	[connection:mysql] select * from `cs_payment_methods` where `cs_payment_methods`.`id` = 1 and `cs_payment_methods`.`deleted_at` is null limit 1 [4.07 ms]
[SQL]	[connection:mysql] insert into `cs_payment_methods` (`shop_id`, `payment_method_name`, `sort`, `status`, `is_default`, `remark`, `updated_at`, `created_at`) values (28, '现金支付', 1, 1, 1, '现金收款', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.82 ms]
[SQL]	[connection:mysql] insert into `cs_payment_methods` (`shop_id`, `payment_method_name`, `sort`, `status`, `is_default`, `remark`, `updated_at`, `created_at`) values (28, '微信支付', 2, 1, 0, '微信扫码支付', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.5 ms]
[SQL]	[connection:mysql] insert into `cs_payment_methods` (`shop_id`, `payment_method_name`, `sort`, `status`, `is_default`, `remark`, `updated_at`, `created_at`) values (28, '支付宝支付', 3, 1, 0, '支付宝扫码支付', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_payment_methods` (`shop_id`, `payment_method_name`, `sort`, `status`, `is_default`, `remark`, `updated_at`, `created_at`) values (28, '银行卡支付', 4, 1, 0, '刷卡支付', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_billing_rules` (`shop_id`, `rule_name`, `rule_type`, `hour_nums`, `price`, `free_minutes`, `sort`, `status`, `updated_at`, `created_at`) values (28, '无桌台费', 0, 0, '0.00', 0, 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.3 ms]
[SQL]	[connection:mysql] insert into `cs_billing_rules` (`shop_id`, `rule_name`, `rule_type`, `hour_nums`, `price`, `free_minutes`, `sort`, `status`, `updated_at`, `created_at`) values (28, '茶位费', 0, 0, '15.00', 0, 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_billing_rules` (`shop_id`, `rule_name`, `rule_type`, `hour_nums`, `price`, `free_minutes`, `sort`, `status`, `updated_at`, `created_at`) values (28, '按小时计费', 1, 0, '25.00', 30, 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.56 ms]
[SQL]	[connection:mysql] insert into `cs_service_rules` (`shop_id`, `rule_name`, `rule_type`, `hour_nums`, `price`, `sort`, `status`, `updated_at`, `created_at`) values (28, '无服务费', 0, 0, '0.00', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.51 ms]
[SQL]	[connection:mysql] insert into `cs_service_rules` (`shop_id`, `rule_name`, `rule_type`, `hour_nums`, `price`, `sort`, `status`, `updated_at`, `created_at`) values (28, '开水茶具费', 0, 0, '3.00', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_service_rules` (`shop_id`, `rule_name`, `rule_type`, `hour_nums`, `price`, `sort`, `status`, `updated_at`, `created_at`) values (28, '包厢服务费', 0, 0, '20.00', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.54 ms]
[SQL]	[connection:mysql] select `id` from `cs_billing_rules` where `shop_id` = 28 and `cs_billing_rules`.`deleted_at` is null order by `id` asc limit 1 [0.84 ms]
[SQL]	[connection:mysql] select `id` from `cs_service_rules` where `shop_id` = 28 and `cs_service_rules`.`deleted_at` is null order by `id` asc limit 1 [0.56 ms]
[SQL]	[connection:mysql] insert into `cs_table_classifications` (`shop_id`, `classification_name`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `updated_at`, `created_at`) values (28, '散座', 48, 55, 2, 0, 0, 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [5.04 ms]
[SQL]	[connection:mysql] insert into `cs_table_classifications` (`shop_id`, `classification_name`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `updated_at`, `created_at`) values (28, '卡座', 48, 55, 4, 0, 80, 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_table_classifications` (`shop_id`, `classification_name`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `updated_at`, `created_at`) values (28, '雅间', 48, 55, 6, 1, 120, 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_table_classifications` (`shop_id`, `classification_name`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `updated_at`, `created_at`) values (28, '贵宾包厢', 48, 55, 10, 1, 300, 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.53 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '名茶系列', 0, '', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.04 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '花草茶', 0, '', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '功夫茶', 0, '', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '茶点小食', 0, '', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.37 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '精品点心', 0, '', 5, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.4 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '时令水果', 0, '', 6, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_goods_categories` (`shop_id`, `category_name`, `parent_id`, `specification_classification_id`, `sort`, `status`, `updated_at`, `created_at`) values (28, '养生汤品', 0, '', 7, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.51 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods_categories` (`shop_id`, `category_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, '茶具配件', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.23 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods_categories` (`shop_id`, `category_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, '茶食搭配', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods_categories` (`shop_id`, `category_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, '茶艺用品', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_specification_classifications` (`shop_id`, `classification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, '茶壶规格', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.12 ms]
[SQL]	[connection:mysql] insert into `cs_specification_classifications` (`shop_id`, `classification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, '份量规格', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '甜度', 0, '无糖,少糖,正常,多糖', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.3 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '冰度', 0, '去冰,少冰,正常冰,多冰', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '口感浓度', 0, '清淡,适中,浓郁', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '温度', 0, '热饮,温饮,冰饮', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '特殊要求', 1, '去奶泡,加柠檬,加蜂蜜,加冰糖,加牛奶,无咖啡因', 5, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '辣度', 0, '不辣,微辣,中辣,特辣', 6, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.49 ms]
[SQL]	[connection:mysql] insert into `cs_goods_remark_labels` (`shop_id`, `remark_label_name`, `label_type`, `remark_label_items`, `sort`, `status`, `updated_at`, `created_at`) values (28, '包装方式', 0, '堂食,打包', 7, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_points_acquisitions` (`shop_id`, `money`, `points`, `updated_at`, `created_at`) values (28, '1.00', 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [3.84 ms]
[SQL]	[connection:mysql] insert into `cs_points_deductions` (`shop_id`, `points`, `money`, `updated_at`, `created_at`) values (28, 100, '1.00', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [3.92 ms]
[SQL]	[connection:mysql] insert into `cs_user_recharge_packages` (`shop_id`, `package_name`, `recharge_amount`, `gift_amount`, `updated_at`, `created_at`) values (28, '100元充值套餐', 100, 10, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.11 ms]
[SQL]	[connection:mysql] insert into `cs_user_recharge_packages` (`shop_id`, `package_name`, `recharge_amount`, `gift_amount`, `updated_at`, `created_at`) values (28, '200元充值套餐', 200, 30, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_user_recharge_packages` (`shop_id`, `package_name`, `recharge_amount`, `gift_amount`, `updated_at`, `created_at`) values (28, '500元充值套餐', 500, 80, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_user_recharge_packages` (`shop_id`, `package_name`, `recharge_amount`, `gift_amount`, `updated_at`, `created_at`) values (28, '1000元充值套餐', 1000, 200, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.44 ms]
[SQL]	[connection:mysql] select * from `cs_specification_classifications` where `shop_id` = 28 and `cs_specification_classifications`.`deleted_at` is null [0.83 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 45, '小壶（1-2人）', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [4.25 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 45, '中壶（3-4人）', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 45, '大壶（5-6人）', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 45, '特大壶（7人以上）', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.36 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 46, '小份', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.34 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 46, '中份', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.38 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 46, '大份', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_specifications` (`shop_id`, `classification_id`, `specification_name`, `sort`, `status`, `updated_at`, `created_at`) values (28, 46, '超大份', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.37 ms]
[SQL]	[connection:mysql] select * from `cs_table_classifications` where `shop_id` = 28 and `cs_table_classifications`.`deleted_at` is null [0.73 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 94, '散座1', '', 48, 55, 2, 0, 0, 1, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [1.24 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 94, '散座2', '', 48, 55, 2, 0, 0, 2, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 94, '散座3', '', 48, 55, 2, 0, 0, 3, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.66 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 94, '散座4', '', 48, 55, 2, 0, 0, 4, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.67 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 94, '散座5', '', 48, 55, 2, 0, 0, 5, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 94, '散座6', '', 48, 55, 2, 0, 0, 6, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.68 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 95, '卡座1', '', 48, 55, 4, 0, 80, 1, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.64 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 95, '卡座2', '', 48, 55, 4, 0, 80, 2, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 95, '卡座3', '', 48, 55, 4, 0, 80, 3, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.68 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 95, '卡座4', '', 48, 55, 4, 0, 80, 4, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.65 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 96, '雅间1', '', 48, 55, 6, 1, 120, 1, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.67 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 96, '雅间2', '', 48, 55, 6, 1, 120, 2, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.81 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 96, '雅间3', '', 48, 55, 6, 1, 120, 3, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.64 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 97, '贵宾包厢1', '', 48, 55, 10, 1, 300, 1, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_tables` (`shop_id`, `classification_id`, `table_name`, `table_qrcode_url`, `billing_rule_id`, `service_rule_id`, `default_custom_nums`, `minimum_type`, `minimum_consumption`, `sort`, `status`, `order_status`, `order_id`, `updated_at`, `created_at`) values (28, 97, '贵宾包厢2', '', 48, 55, 10, 1, 300, 2, 1, 0, 0, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.7 ms]
[SQL]	[connection:mysql] select * from `cs_goods_categories` where `shop_id` = 28 and `cs_goods_categories`.`deleted_at` is null [1.03 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 148, '西湖龙井', '壶', 'G148001', '', '', '88.00', '45.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>西湖龙井，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [11.72 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 148, '碧螺春', '壶', 'G148002', '', '', '78.00', '40.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>碧螺春，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.56 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 148, '铁观音', '壶', 'G148003', '', '', '98.00', '50.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>铁观音，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.56 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 148, '大红袍', '壶', 'G148004', '', '', '128.00', '65.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>大红袍，精心制作，品质保证。</p>', '[]', '[]', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.57 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 149, '玫瑰花茶', '壶', 'G149001', '', '', '58.00', '28.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>玫瑰花茶，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.55 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 149, '茉莉花茶', '壶', 'G149002', '', '', '48.00', '25.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>茉莉花茶，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.76 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 149, '薰衣草茶', '壶', 'G149003', '', '', '68.00', '35.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>薰衣草茶，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.55 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 149, '洋甘菊茶', '壶', 'G149004', '', '', '55.00', '30.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>洋甘菊茶，精心制作，品质保证。</p>', '[]', '[]', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.75 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 150, '单枞茶', '壶', 'G150001', '', '', '108.00', '55.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>单枞茶，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.61 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 150, '岩茶', '壶', 'G150002', '', '', '118.00', '60.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>岩茶，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.6 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 150, '普洱生茶', '壶', 'G150003', '', '', '98.00', '50.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>普洱生茶，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.59 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 150, '普洱熟茶', '壶', 'G150004', '', '', '88.00', '45.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>普洱熟茶，精心制作，品质保证。</p>', '[]', '[]', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.62 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 151, '绿豆糕', '份', 'G151001', '', '', '18.00', '8.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>绿豆糕，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.57 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 151, '核桃酥', '份', 'G151002', '', '', '22.00', '12.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>核桃酥，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.55 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 151, '瓜子仁', '份', 'G151003', '', '', '15.00', '7.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>瓜子仁，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.56 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 151, '花生米', '份', 'G151004', '', '', '12.00', '6.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>花生米，精心制作，品质保证。</p>', '[]', '[]', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.63 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 152, '蛋黄酥', '份', 'G152001', '', '', '35.00', '18.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>蛋黄酥，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.61 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 152, '凤梨酥', '份', 'G152002', '', '', '38.00', '20.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>凤梨酥，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.61 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 152, '牛轧糖', '份', 'G152003', '', '', '28.00', '15.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>牛轧糖，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.62 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 152, '桂花糕', '份', 'G152004', '', '', '32.00', '16.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>桂花糕，精心制作，品质保证。</p>', '[]', '[]', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.62 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 153, '时令水果拼盘', '份', 'G153001', '', '', '58.00', '28.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>时令水果拼盘，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.64 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 153, '进口水果拼盘', '份', 'G153002', '', '', '88.00', '45.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>进口水果拼盘，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.62 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 153, '季节鲜果茶', '杯', 'G153003', '', '', '48.00', '25.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>季节鲜果茶，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.59 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 154, '银耳莲子汤', '份', 'G154001', '', '', '38.00', '18.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>银耳莲子汤，精心制作，品质保证。</p>', '[]', '[]', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.71 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 154, '红豆薏米汤', '份', 'G154002', '', '', '35.00', '16.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>红豆薏米汤，精心制作，品质保证。</p>', '[]', '[]', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.57 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 154, '百合雪梨汤', '份', 'G154003', '', '', '42.00', '20.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>百合雪梨汤，精心制作，品质保证。</p>', '[]', '[]', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.54 ms]
[SQL]	[connection:mysql] insert into `cs_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `min_stock`, `max_stock`, `inventory_management`, `specification_type`, `specification_item_list`, `specification_data_list`, `content`, `accessories_goods_ids`, `remark_label_ids`, `sort`, `status`, `updated_at`, `created_at`) values (28, 154, '冰糖燕窝', '份', 'G154004', '', '', '188.00', '98.00', 100, 10, 500, 1, 0, '[]', '[]', '<p>冰糖燕窝，精心制作，品质保证。</p>', '[]', '[]', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.61 ms]
[SQL]	[connection:mysql] select * from `cs_accessories_goods_categories` where `shop_id` = 28 and `cs_accessories_goods_categories`.`deleted_at` is null [0.8 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 61, '紫砂茶壶', '个', 'A61001', '', '168.00', '85.00', 50, 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [5.34 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 61, '公道杯', '个', 'A61002', '', '58.00', '30.00', 50, 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.49 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 61, '品茗杯', '个', 'A61003', '', '38.00', '20.00', 50, 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 61, '茶漏', '个', 'A61004', '', '28.00', '15.00', 50, 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 61, '茶夹', '个', 'A61005', '', '25.00', '12.00', 50, 5, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 61, '茶盘', '个', 'A61006', '', '88.00', '45.00', 50, 6, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.5 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 62, '蜂蜜', '瓶', 'A62001', '', '28.00', '15.00', 50, 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.58 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 62, '柠檬片', '份', 'A62002', '', '18.00', '8.00', 50, 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.53 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 62, '冰糖', '份', 'A62003', '', '15.00', '7.00', 50, 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.54 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 62, '红糖', '份', 'A62004', '', '12.00', '6.00', 50, 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.51 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 62, '牛奶', '份', 'A62005', '', '8.00', '4.00', 50, 5, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.66 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 62, '奶泡', '份', 'A62006', '', '5.00', '2.00', 50, 6, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.6 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 63, '茶巾', '条', 'A63001', '', '18.00', '8.00', 50, 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.54 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 63, '竹茶匙', '个', 'A63002', '', '15.00', '7.00', 50, 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.52 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 63, '茶叶罐', '个', 'A63003', '', '68.00', '35.00', 50, 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.59 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 63, '香炉', '个', 'A63004', '', '98.00', '50.00', 50, 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.54 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 63, '茶席', '套', 'A63005', '', '128.00', '65.00', 50, 5, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.53 ms]
[SQL]	[connection:mysql] insert into `cs_accessories_goods` (`shop_id`, `category_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_price`, `cost_price`, `inventory_nums`, `sort`, `status`, `updated_at`, `created_at`) values (28, 63, '花瓶', '个', 'A63006', '', '88.00', '45.00', 50, 6, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.55 ms]
[SQL]	[connection:mysql] select `id`, `goods_name`, `goods_price` from `cs_goods` where `shop_id` = 28 and `cs_goods`.`deleted_at` is null [0.94 ms]
[SQL]	[connection:mysql] insert into `cs_set_meal_goods` (`shop_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `goods_data_list`, `content`, `sort`, `status`, `updated_at`, `created_at`) values (28, '下午茶双人套餐', '套', 'SET001', '', '[]', '168.00', '85.00', 30, '[{"goods_id":441,"goods_name":"\u897f\u6e56\u9f99\u4e95","quantity":1,"price":"88.00"},{"goods_id":446,"goods_name":"\u8309\u8389\u82b1\u8336","quantity":1,"price":"48.00"},{"goods_id":457,"goods_name":"\u86cb\u9ec4\u9165","quantity":1,"price":"35.00"},{"goods_id":458,"goods_name":"\u51e4\u68a8\u9165","quantity":1,"price":"38.00"},{"goods_id":461,"goods_name":"\u65f6\u4ee4\u6c34\u679c\u62fc\u76d8","quantity":1,"price":"58.00"}]', '<p>包含：精选茶品2壶、精美点心4样、时令水果拼盘1份</p>', 1, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [5.86 ms]
[SQL]	[connection:mysql] insert into `cs_set_meal_goods` (`shop_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `goods_data_list`, `content`, `sort`, `status`, `updated_at`, `created_at`) values (28, '商务茶歇套餐', '套', 'SET002', '', '[]', '288.00', '145.00', 30, '[{"goods_id":444,"goods_name":"\u5927\u7ea2\u888d","quantity":1,"price":"128.00"},{"goods_id":443,"goods_name":"\u94c1\u89c2\u97f3","quantity":1,"price":"98.00"},{"goods_id":442,"goods_name":"\u78a7\u87ba\u6625","quantity":1,"price":"78.00"},{"goods_id":460,"goods_name":"\u6842\u82b1\u7cd5","quantity":2,"price":"32.00"},{"goods_id":459,"goods_name":"\u725b\u8f67\u7cd6","quantity":2,"price":"28.00"},{"goods_id":464,"goods_name":"\u94f6\u8033\u83b2\u5b50\u6c64","quantity":2,"price":"38.00"}]', '<p>包含：名茶3壶、精品茶点6样、养生汤品2份</p>', 2, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.55 ms]
[SQL]	[connection:mysql] insert into `cs_set_meal_goods` (`shop_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `goods_data_list`, `content`, `sort`, `status`, `updated_at`, `created_at`) values (28, '情侣浪漫套餐', '套', 'SET003', '', '[]', '228.00', '115.00', 30, '[{"goods_id":445,"goods_name":"\u73ab\u7470\u82b1\u8336","quantity":1,"price":"58.00"},{"goods_id":447,"goods_name":"\u85b0\u8863\u8349\u8336","quantity":1,"price":"68.00"},{"goods_id":457,"goods_name":"\u86cb\u9ec4\u9165","quantity":2,"price":"35.00"},{"goods_id":458,"goods_name":"\u51e4\u68a8\u9165","quantity":2,"price":"38.00"},{"goods_id":462,"goods_name":"\u8fdb\u53e3\u6c34\u679c\u62fc\u76d8","quantity":1,"price":"88.00"}]', '<p>包含：花草茶2壶、浪漫点心4样、进口水果拼盘1份、香薰用品</p>', 3, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.74 ms]
[SQL]	[connection:mysql] insert into `cs_set_meal_goods` (`shop_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `goods_data_list`, `content`, `sort`, `status`, `updated_at`, `created_at`) values (28, '家庭聚会套餐', '套', 'SET004', '', '[]', '398.00', '200.00', 30, '[{"goods_id":441,"goods_name":"\u897f\u6e56\u9f99\u4e95","quantity":2,"price":"88.00"},{"goods_id":452,"goods_name":"\u666e\u6d31\u719f\u8336","quantity":2,"price":"88.00"},{"goods_id":446,"goods_name":"\u8309\u8389\u82b1\u8336","quantity":1,"price":"48.00"},{"goods_id":453,"goods_name":"\u7eff\u8c46\u7cd5","quantity":2,"price":"18.00"},{"goods_id":454,"goods_name":"\u6838\u6843\u9165","quantity":2,"price":"22.00"},{"goods_id":464,"goods_name":"\u94f6\u8033\u83b2\u5b50\u6c64","quantity":3,"price":"38.00"},{"goods_id":461,"goods_name":"\u65f6\u4ee4\u6c34\u679c\u62fc\u76d8","quantity":2,"price":"58.00"}]', '<p>包含：多种茶品5壶、丰富茶点8样、养生汤品3份、时令水果</p>', 4, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.59 ms]
[SQL]	[connection:mysql] insert into `cs_set_meal_goods` (`shop_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `goods_data_list`, `content`, `sort`, `status`, `updated_at`, `created_at`) values (28, '养生滋补套餐', '套', 'SET005', '', '[]', '328.00', '165.00', 30, '[{"goods_id":448,"goods_name":"\u6d0b\u7518\u83ca\u8336","quantity":1,"price":"55.00"},{"goods_id":451,"goods_name":"\u666e\u6d31\u751f\u8336","quantity":2,"price":"98.00"},{"goods_id":467,"goods_name":"\u51b0\u7cd6\u71d5\u7a9d","quantity":1,"price":"188.00"},{"goods_id":464,"goods_name":"\u94f6\u8033\u83b2\u5b50\u6c64","quantity":2,"price":"38.00"},{"goods_id":466,"goods_name":"\u767e\u5408\u96ea\u68a8\u6c64","quantity":1,"price":"42.00"},{"goods_id":460,"goods_name":"\u6842\u82b1\u7cd5","quantity":3,"price":"32.00"}]', '<p>包含：养生茶品3壶、滋补汤品4份、健康茶点5样</p>', 5, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.58 ms]
[SQL]	[connection:mysql] insert into `cs_set_meal_goods` (`shop_id`, `goods_name`, `unit_name`, `goods_code`, `goods_thumb`, `goods_thumbs`, `goods_price`, `cost_price`, `inventory_nums`, `goods_data_list`, `content`, `sort`, `status`, `updated_at`, `created_at`) values (28, '功夫茶体验套餐', '套', 'SET006', '', '[]', '268.00', '135.00', 30, '[{"goods_id":449,"goods_name":"\u5355\u679e\u8336","quantity":1,"price":"108.00"},{"goods_id":450,"goods_name":"\u5ca9\u8336","quantity":1,"price":"118.00"},{"goods_id":451,"goods_name":"\u666e\u6d31\u751f\u8336","quantity":1,"price":"98.00"},{"goods_id":452,"goods_name":"\u666e\u6d31\u719f\u8336","quantity":1,"price":"88.00"},{"goods_id":457,"goods_name":"\u86cb\u9ec4\u9165","quantity":3,"price":"35.00"},{"goods_id":459,"goods_name":"\u725b\u8f67\u7cd6","quantity":3,"price":"28.00"}]', '<p>包含：功夫茶4壶、茶艺用品体验、精选茶点6样</p>', 6, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [0.54 ms]
[SQL]	[connection:mysql] select * from `cs_goods` where `shop_id` = 28 and `cs_goods`.`deleted_at` is null [1.24 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 441 and `goods_type` = 1 limit 1 [5.84 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 441, 1, 100, 10, 500, 45, 4500, 1, '2025-08-02 12:57:04', '2025-08-02 12:57:04') [2.28 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 441, 1, 1, 100, 0, 100, 45, 4500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:04', '', '2025-08-02 12:57:04', '2025-08-02 12:57:04') [9.14 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 442 and `goods_type` = 1 limit 1 [0.77 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 442, 1, 100, 10, 500, 40, 4000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.67 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 442, 1, 1, 100, 0, 100, 40, 4000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.79 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 443 and `goods_type` = 1 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 443, 1, 100, 10, 500, 50, 5000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.51 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.54 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 443, 1, 1, 100, 0, 100, 50, 5000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.6 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 444 and `goods_type` = 1 limit 1 [0.66 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 444, 1, 100, 10, 500, 65, 6500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.82 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 444, 1, 1, 100, 0, 100, 65, 6500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.01 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 445 and `goods_type` = 1 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 445, 1, 100, 10, 500, 28, 2800, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.48 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.42 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 445, 1, 1, 100, 0, 100, 28, 2800, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 446 and `goods_type` = 1 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 446, 1, 100, 10, 500, 25, 2500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 446, 1, 1, 100, 0, 100, 25, 2500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.77 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 447 and `goods_type` = 1 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 447, 1, 100, 10, 500, 35, 3500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.51 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.51 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 447, 1, 1, 100, 0, 100, 35, 3500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.69 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 448 and `goods_type` = 1 limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 448, 1, 100, 10, 500, 30, 3000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.65 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.51 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 448, 1, 1, 100, 0, 100, 30, 3000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.73 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 449 and `goods_type` = 1 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 449, 1, 100, 10, 500, 55, 5500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 449, 1, 1, 100, 0, 100, 55, 5500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.77 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 450 and `goods_type` = 1 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 450, 1, 100, 10, 500, 60, 6000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.54 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.61 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 450, 1, 1, 100, 0, 100, 60, 6000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.28 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 451 and `goods_type` = 1 limit 1 [0.67 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 451, 1, 100, 10, 500, 50, 5000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.81 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.75 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 451, 1, 1, 100, 0, 100, 50, 5000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.26 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 452 and `goods_type` = 1 limit 1 [0.63 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 452, 1, 100, 10, 500, 45, 4500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.72 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.59 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 452, 1, 1, 100, 0, 100, 45, 4500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.26 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 453 and `goods_type` = 1 limit 1 [0.61 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 453, 1, 100, 10, 500, 8, 800, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.59 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 453, 1, 1, 100, 0, 100, 8, 800, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.16 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 454 and `goods_type` = 1 limit 1 [0.72 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 454, 1, 100, 10, 500, 12, 1200, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.67 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 454, 1, 1, 100, 0, 100, 12, 1200, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.37 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 455 and `goods_type` = 1 limit 1 [0.64 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 455, 1, 100, 10, 500, 7, 700, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.73 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 455, 1, 1, 100, 0, 100, 7, 700, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.42 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 456 and `goods_type` = 1 limit 1 [0.63 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 456, 1, 100, 10, 500, 6, 600, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.7 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 456, 1, 1, 100, 0, 100, 6, 600, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.53 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 457 and `goods_type` = 1 limit 1 [0.42 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 457, 1, 100, 10, 500, 18, 1800, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.66 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.53 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 457, 1, 1, 100, 0, 100, 18, 1800, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.92 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 458 and `goods_type` = 1 limit 1 [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 458, 1, 100, 10, 500, 20, 2000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 458, 1, 1, 100, 0, 100, 20, 2000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.95 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 459 and `goods_type` = 1 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 459, 1, 100, 10, 500, 15, 1500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.49 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 459, 1, 1, 100, 0, 100, 15, 1500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.96 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 460 and `goods_type` = 1 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 460, 1, 100, 10, 500, 16, 1600, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.66 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 460, 1, 1, 100, 0, 100, 16, 1600, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.94 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 461 and `goods_type` = 1 limit 1 [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 461, 1, 100, 10, 500, 28, 2800, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 461, 1, 1, 100, 0, 100, 28, 2800, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.98 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 462 and `goods_type` = 1 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 462, 1, 100, 10, 500, 45, 4500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.52 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.52 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 462, 1, 1, 100, 0, 100, 45, 4500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 463 and `goods_type` = 1 limit 1 [0.42 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 463, 1, 100, 10, 500, 25, 2500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.6 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 463, 1, 1, 100, 0, 100, 25, 2500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.07 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 464 and `goods_type` = 1 limit 1 [0.49 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 464, 1, 100, 10, 500, 18, 1800, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.47 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 464, 1, 1, 100, 0, 100, 18, 1800, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.03 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 465 and `goods_type` = 1 limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 465, 1, 100, 10, 500, 16, 1600, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.42 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 465, 1, 1, 100, 0, 100, 16, 1600, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.99 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 466 and `goods_type` = 1 limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 466, 1, 100, 10, 500, 20, 2000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.45 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 466, 1, 1, 100, 0, 100, 20, 2000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.98 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 467 and `goods_type` = 1 limit 1 [0.41 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 467, 1, 100, 10, 500, 98, 9800, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 467, 1, 1, 100, 0, 100, 98, 9800, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.07 ms]
[SQL]	[connection:mysql] select * from `cs_accessories_goods` where `shop_id` = 28 and `cs_accessories_goods`.`deleted_at` is null [0.96 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 292 and `goods_type` = 2 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 292, 2, 50, 0, 0, 85, 4250, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.48 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 292, 2, 1, 50, 0, 50, 85, 4250, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.03 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 293 and `goods_type` = 2 limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 293, 2, 50, 0, 0, 30, 1500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.49 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 293, 2, 1, 50, 0, 50, 30, 1500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.05 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 294 and `goods_type` = 2 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 294, 2, 50, 0, 0, 20, 1000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.46 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 294, 2, 1, 50, 0, 50, 20, 1000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.12 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 295 and `goods_type` = 2 limit 1 [0.47 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 295, 2, 50, 0, 0, 15, 750, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.48 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.41 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 295, 2, 1, 50, 0, 50, 15, 750, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.11 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 296 and `goods_type` = 2 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 296, 2, 50, 0, 0, 12, 600, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.45 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 296, 2, 1, 50, 0, 50, 12, 600, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.12 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 297 and `goods_type` = 2 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 297, 2, 50, 0, 0, 45, 2250, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.47 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.42 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 297, 2, 1, 50, 0, 50, 45, 2250, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.14 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 298 and `goods_type` = 2 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 298, 2, 50, 0, 0, 15, 750, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.48 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.57 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 298, 2, 1, 50, 0, 50, 15, 750, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.31 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 299 and `goods_type` = 2 limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 299, 2, 50, 0, 0, 8, 400, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.45 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 299, 2, 1, 50, 0, 50, 8, 400, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.21 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 300 and `goods_type` = 2 limit 1 [0.41 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 300, 2, 50, 0, 0, 7, 350, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.42 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.4 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 300, 2, 1, 50, 0, 50, 7, 350, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.15 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 301 and `goods_type` = 2 limit 1 [0.41 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 301, 2, 50, 0, 0, 6, 300, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.48 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 301, 2, 1, 50, 0, 50, 6, 300, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.34 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 302 and `goods_type` = 2 limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 302, 2, 50, 0, 0, 4, 200, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.52 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 302, 2, 1, 50, 0, 50, 4, 200, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.34 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 303 and `goods_type` = 2 limit 1 [0.62 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 303, 2, 50, 0, 0, 2, 100, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 303, 2, 1, 50, 0, 50, 2, 100, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.44 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 304 and `goods_type` = 2 limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 304, 2, 50, 0, 0, 8, 400, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 304, 2, 1, 50, 0, 50, 8, 400, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.39 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 305 and `goods_type` = 2 limit 1 [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 305, 2, 50, 0, 0, 7, 350, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.51 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.48 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 305, 2, 1, 50, 0, 50, 7, 350, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.37 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 306 and `goods_type` = 2 limit 1 [0.57 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 306, 2, 50, 0, 0, 35, 1750, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.52 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 306, 2, 1, 50, 0, 50, 35, 1750, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.35 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 307 and `goods_type` = 2 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 307, 2, 50, 0, 0, 50, 2500, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.51 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 307, 2, 1, 50, 0, 50, 50, 2500, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.42 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 308 and `goods_type` = 2 limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 308, 2, 50, 0, 0, 65, 3250, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.51 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.52 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 308, 2, 1, 50, 0, 50, 65, 3250, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.45 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 309 and `goods_type` = 2 limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 309, 2, 50, 0, 0, 45, 2250, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.51 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.46 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 309, 2, 1, 50, 0, 50, 45, 2250, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.51 ms]
[SQL]	[connection:mysql] select * from `cs_set_meal_goods` where `shop_id` = 28 and `cs_set_meal_goods`.`deleted_at` is null [0.92 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 98 and `goods_type` = 3 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 98, 3, 30, 0, 0, 85, 2550, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.53 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 98, 3, 1, 30, 0, 30, 85, 2550, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.38 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 99 and `goods_type` = 3 limit 1 [0.4 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 99, 3, 30, 0, 0, 145, 4350, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.5 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.45 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 99, 3, 1, 30, 0, 30, 145, 4350, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.43 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 100 and `goods_type` = 3 limit 1 [0.43 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 100, 3, 30, 0, 0, 115, 3450, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.58 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.42 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 100, 3, 1, 30, 0, 30, 115, 3450, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.46 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 101 and `goods_type` = 3 limit 1 [0.52 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 101, 3, 30, 0, 0, 200, 6000, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.74 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.53 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 101, 3, 1, 30, 0, 30, 200, 6000, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.52 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 102 and `goods_type` = 3 limit 1 [0.68 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 102, 3, 30, 0, 0, 165, 4950, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.59 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.51 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 102, 3, 1, 30, 0, 30, 165, 4950, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.5 ms]
[SQL]	[connection:mysql] select * from `cs_inventory` where `shop_id` = 28 and `goods_id` = 103 and `goods_type` = 3 limit 1 [0.44 ms]
[SQL]	[connection:mysql] insert into `cs_inventory` (`shop_id`, `goods_id`, `goods_type`, `current_stock`, `min_stock`, `max_stock`, `cost_price`, `total_cost`, `status`, `updated_at`, `created_at`) values (28, 103, 3, 30, 0, 0, 135, 4050, 1, '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.54 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where `id` = '18' and `cs_shop_admins`.`deleted_at` is null limit 1 [0.49 ms]
[SQL]	[connection:mysql] insert into `cs_inventory_log` (`shop_id`, `goods_id`, `goods_type`, `operation_type`, `operation_nums`, `before_stock`, `after_stock`, `cost_price`, `cost_change`, `operation_reason`, `relation_type`, `relation_id`, `operator_id`, `operator_name`, `operation_time`, `remark`, `updated_at`, `created_at`) values (28, 103, 3, 1, 30, 0, 30, 135, 4050, '初始化库存', 'manual', 0, '18', 'System', '2025-08-02 12:57:05', '', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [1.51 ms]
[SQL]	[connection:mysql] insert into `cs_print_configs` (`shop_id`, `printer_name`, `printer_type`, `connection_type`, `printer_ip`, `printer_port`, `device_name`, `is_default`, `status`, `print_speed`, `paper_size`, `auto_cut`, `remark`, `updated_at`, `created_at`) values (28, '收银台打印机', 1, 1, '', 0, 'POS-80', 1, 1, 5, '80mm', 1, '收银台默认打印机，用于打印订单小票和收据', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [7.34 ms]
[SQL]	[connection:mysql] insert into `cs_print_configs` (`shop_id`, `printer_name`, `printer_type`, `connection_type`, `printer_ip`, `printer_port`, `device_name`, `is_default`, `status`, `print_speed`, `paper_size`, `auto_cut`, `remark`, `updated_at`, `created_at`) values (28, '后厨打印机', 2, 2, '*************', 9100, 'Kitchen-Printer', 1, 0, 6, '80mm', 1, '后厨默认打印机，用于打印厨房订单和备餐单据', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.52 ms]
[SQL]	[connection:mysql] insert into `cs_print_templates` (`shop_id`, `template_name`, `template_type`, `template_content`, `paper_size`, `font_size`, `is_default`, `status`, `sort`, `remark`, `updated_at`, `created_at`) values (28, '订单小票模板', 1, '"[{\"id\":\"header_1\",\"type\":\"title\",\"text\":\"{{shop_name}}\",\"style\":{\"align\":\"left\",\"bold\":true,\"fontSize\":18}},{\"id\":\"line_1\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"left\"}},{\"id\":\"info_1\",\"type\":\"text\",\"text\":\"\\u8ba2\\u5355\\u53f7\\uff1a{{order_no}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"info_2\",\"type\":\"text\",\"text\":\"\\u684c\\u53f0\\u53f7\\uff1a{{classification_name}} - {{table_name}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"info_3\",\"type\":\"text\",\"text\":\"\\u7528\\u9910\\u4eba\\u6570\\uff1a{{customer_nums}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"member_info\",\"type\":\"text\",\"text\":\"\\u4f1a\\u5458\\uff1a{{user_name}}\\uff08{{user_level_name}}\\uff09\",\"style\":{\"align\":\"left\"}},{\"id\":\"line_2\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"left\"}},{\"id\":\"goods_table\",\"type\":\"table\",\"data\":[[\"\\u5546\\u54c1\",\"\\u6570\\u91cf\",\"\\u5355\\u4ef7\",\"\\u91d1\\u989d\"],[\"{{goods_name}}\",\"{{goods_nums}}\",\"{{goods_price}}\",\"{{subtotal}}\"]],\"style\":{\"align\":\"left\"}},{\"id\":\"line_3\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_1\",\"type\":\"text\",\"text\":\"\\u5546\\u54c1\\u5c0f\\u8ba1\\uff1a\\uffe5{{goods_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_2\",\"type\":\"text\",\"text\":\"\\u684c\\u53f0\\u8d39\\uff1a\\uffe5{{billing_fee}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_3\",\"type\":\"text\",\"text\":\"\\u670d\\u52a1\\u8d39\\uff1a\\uffe5{{service_fee}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_4\",\"type\":\"text\",\"text\":\"\\u4f1a\\u5458\\u5546\\u54c1\\u4f18\\u60e0\\uff1a\\uffe5{{user_discount_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_5\",\"type\":\"text\",\"text\":\"\\u4f1a\\u5458\\u684c\\u53f0\\u4f18\\u60e0\\uff1a\\uffe5{{billing_fee_discount_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_6\",\"type\":\"text\",\"text\":\"\\u4f1a\\u5458\\u670d\\u52a1\\u8d39\\u4f18\\u60e0\\uff1a\\uffe5{{service_fee_discount_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_7\",\"type\":\"text\",\"text\":\"\\u6574\\u5355\\u6298\\u6263\\u4f18\\u60e0\\uff1a\\uffe5{{discount_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_8\",\"type\":\"text\",\"text\":\"\\u6d3b\\u52a8\\u4f18\\u60e0\\uff1a\\uffe5{{marketing_discount_amount}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_9\",\"type\":\"text\",\"text\":\"\\u624b\\u52a8\\u4f18\\u60e0\\uff1a\\uffe5{{staff_discount_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"amount_10\",\"type\":\"text\",\"text\":\"\\u603b\\u4f18\\u60e0\\u91d1\\u989d\\uff1a\\uffe5{{total_discount_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"total_1\",\"type\":\"text\",\"text\":\"\\u5b9e\\u4ed8\\u91d1\\u989d\\uff1a\\uffe5{{real_pay_money}}\",\"style\":{\"align\":\"left\",\"bold\":true,\"fontSize\":16}},{\"id\":\"line_4\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"left\"}},{\"id\":\"pay_1\",\"type\":\"text\",\"text\":\"\\u652f\\u4ed8\\u65b9\\u5f0f\\uff1a{{payment_method_name}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"pay_2\",\"type\":\"text\",\"text\":\"\\u5b9e\\u6536\\u91d1\\u989d\\uff1a\\uffe5{{real_pay_money}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"line_5\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_1\",\"type\":\"text\",\"text\":\"\\u4e0b\\u5355\\u65f6\\u95f4\\uff1a{{create_time}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_2\",\"type\":\"text\",\"text\":\"\\u652f\\u4ed8\\u65f6\\u95f4\\uff1a{{pay_time}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_3\",\"type\":\"text\",\"text\":\"\\u6253\\u5370\\u65f6\\u95f4\\uff1a{{print_time}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_4\",\"type\":\"text\",\"text\":\"\\u6536\\u94f6\\u5458\\uff1a{{shoukuan_admin_name}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_5\",\"type\":\"text\",\"text\":\"\\u95e8\\u5e97\\u5730\\u5740\\uff1a{{shop_address}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_6\",\"type\":\"text\",\"text\":\"\\u95e8\\u5e97\\u7535\\u8bdd\\uff1a{{shop_phone}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_7\",\"type\":\"text\",\"text\":\"\\u8c22\\u8c22\\u60e0\\u987e\\uff0c\\u6b22\\u8fce\\u518d\\u6b21\\u5149\\u4e34\\uff01\",\"style\":{\"align\":\"left\",\"bold\":true}}]"', '80mm', 12, 1, 1, 1, '收银台订单小票打印模板，包含完整订单信息和会员优惠明细', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [9.22 ms]
[SQL]	[connection:mysql] insert into `cs_print_templates` (`shop_id`, `template_name`, `template_type`, `template_content`, `paper_size`, `font_size`, `is_default`, `status`, `sort`, `remark`, `updated_at`, `created_at`) values (28, '后厨订单模板', 2, '"[{\"id\":\"header_1\",\"type\":\"title\",\"text\":\"\\u540e\\u53a8\\u8ba2\\u5355\\u5355\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":20}},{\"id\":\"line_1\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"info_1\",\"type\":\"text\",\"text\":\"\\u8ba2\\u5355\\u53f7\\uff1a{{order_no}}\",\"style\":{\"align\":\"left\",\"bold\":true,\"fontSize\":16}},{\"id\":\"info_2\",\"type\":\"text\",\"text\":\"\\u684c\\u53f0\\u53f7\\uff1a{{table_name}}\",\"style\":{\"align\":\"left\",\"bold\":true,\"fontSize\":16}},{\"id\":\"info_3\",\"type\":\"text\",\"text\":\"\\u7528\\u9910\\u4eba\\u6570\\uff1a{{customer_count}}\\u4eba\",\"style\":{\"align\":\"left\",\"fontSize\":14}},{\"id\":\"info_4\",\"type\":\"text\",\"text\":\"\\u4e0b\\u5355\\u65f6\\u95f4\\uff1a{{create_time}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"info_5\",\"type\":\"text\",\"text\":\"\\u7d27\\u6025\\u7a0b\\u5ea6\\uff1a{{urgency}}\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"line_2\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"goods_title\",\"type\":\"text\",\"text\":\"\\u5236\\u4f5c\\u6e05\\u5355\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":16}},{\"id\":\"goods_table\",\"type\":\"table\",\"data\":[[\"\\u5546\\u54c1\\u540d\\u79f0\",\"\\u6570\\u91cf\",\"\\u5907\\u6ce8\"],[\"{{name}}\",\"{{num}}\",\"{{remark}}\"]],\"style\":{\"align\":\"left\",\"fontSize\":14}},{\"id\":\"line_3\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"special_title\",\"type\":\"text\",\"text\":\"\\u7279\\u6b8a\\u8981\\u6c42\\uff1a\",\"style\":{\"align\":\"left\",\"bold\":true,\"fontSize\":14}},{\"id\":\"special_content\",\"type\":\"text\",\"text\":\"{{special_requirements}}\",\"style\":{\"align\":\"left\",\"fontSize\":14}},{\"id\":\"line_4\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"footer_1\",\"type\":\"text\",\"text\":\"\\u6253\\u5370\\u65f6\\u95f4\\uff1a{{print_time}}\",\"style\":{\"align\":\"center\"}}]"', '80mm', 14, 1, 1, 2, '后厨订单打印模板，突出制作要求和特殊备注', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.78 ms]
[SQL]	[connection:mysql] insert into `cs_print_templates` (`shop_id`, `template_name`, `template_type`, `template_content`, `paper_size`, `font_size`, `is_default`, `status`, `sort`, `remark`, `updated_at`, `created_at`) values (28, '预约单模板', 3, '"[{\"id\":\"header_1\",\"type\":\"title\",\"text\":\"{{shop_name}}\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":18}},{\"id\":\"header_2\",\"type\":\"title\",\"text\":\"\\u9884\\u7ea6\\u786e\\u8ba4\\u5355\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":16}},{\"id\":\"line_1\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"info_1\",\"type\":\"text\",\"text\":\"\\u9884\\u7ea6\\u53f7\\uff1a{{reservation_no}}\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"info_2\",\"type\":\"text\",\"text\":\"\\u5ba2\\u6237\\u59d3\\u540d\\uff1a{{customer_name}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"info_3\",\"type\":\"text\",\"text\":\"\\u8054\\u7cfb\\u7535\\u8bdd\\uff1a{{customer_phone}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"info_4\",\"type\":\"text\",\"text\":\"\\u9884\\u7ea6\\u65e5\\u671f\\uff1a{{reservation_date}}\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"info_5\",\"type\":\"text\",\"text\":\"\\u9884\\u7ea6\\u65f6\\u95f4\\uff1a{{reservation_time}}\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"info_6\",\"type\":\"text\",\"text\":\"\\u9884\\u7ea6\\u684c\\u53f0\\uff1a{{table_name}}\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"info_7\",\"type\":\"text\",\"text\":\"\\u9884\\u7ea6\\u4eba\\u6570\\uff1a{{guest_count}}\\u4eba\",\"style\":{\"align\":\"left\"}},{\"id\":\"line_2\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"special_title\",\"type\":\"text\",\"text\":\"\\u7279\\u6b8a\\u8981\\u6c42\\uff1a\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"special_content\",\"type\":\"text\",\"text\":\"{{special_requirements}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"line_3\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"footer_1\",\"type\":\"text\",\"text\":\"\\u9884\\u7ea6\\u65f6\\u95f4\\uff1a{{create_time}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_2\",\"type\":\"text\",\"text\":\"\\u8bf7\\u51c6\\u65f6\\u5230\\u5e97\\uff0c\\u5982\\u9700\\u53d6\\u6d88\\u8bf7\\u63d0\\u524d\\u8054\\u7cfb\",\"style\":{\"align\":\"center\",\"bold\":true}}]"', '80mm', 12, 1, 1, 3, '预约单打印模板，用于打印客户预约确认单', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.6 ms]
[SQL]	[connection:mysql] insert into `cs_print_templates` (`shop_id`, `template_name`, `template_type`, `template_content`, `paper_size`, `font_size`, `is_default`, `status`, `sort`, `remark`, `updated_at`, `created_at`) values (28, '日结报表模板', 4, '"[{\"id\":\"header_1\",\"type\":\"title\",\"text\":\"{{shop_name}}\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":18}},{\"id\":\"header_2\",\"type\":\"title\",\"text\":\"\\u8425\\u4e1a\\u65e5\\u62a5\\u8868\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":16}},{\"id\":\"header_3\",\"type\":\"text\",\"text\":\"\\u7edf\\u8ba1\\u65e5\\u671f\\uff1a{{report_date}}\",\"style\":{\"align\":\"center\"}},{\"id\":\"line_1\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"sales_title\",\"type\":\"text\",\"text\":\"\\u8425\\u4e1a\\u6c47\\u603b\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":14}},{\"id\":\"sales_1\",\"type\":\"text\",\"text\":\"\\u8ba2\\u5355\\u603b\\u6570\\uff1a{{total_orders}}\\u5355\",\"style\":{\"align\":\"left\"}},{\"id\":\"sales_2\",\"type\":\"text\",\"text\":\"\\u8425\\u4e1a\\u603b\\u989d\\uff1a\\uffe5{{total_amount}}\",\"style\":{\"align\":\"left\",\"bold\":true}},{\"id\":\"sales_3\",\"type\":\"text\",\"text\":\"\\u73b0\\u91d1\\u6536\\u5165\\uff1a\\uffe5{{cash_amount}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"sales_4\",\"type\":\"text\",\"text\":\"\\u5237\\u5361\\u6536\\u5165\\uff1a\\uffe5{{card_amount}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"sales_5\",\"type\":\"text\",\"text\":\"\\u7ebf\\u4e0a\\u652f\\u4ed8\\uff1a\\uffe5{{online_amount}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"line_2\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"goods_title\",\"type\":\"text\",\"text\":\"\\u5546\\u54c1\\u6c47\\u603b\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":14}},{\"id\":\"goods_table\",\"type\":\"table\",\"data\":[[\"\\u5546\\u54c1\\u540d\\u79f0\",\"\\u6570\\u91cf\",\"\\u91d1\\u989d\"],[\"{{goods_name}}\",\"{{quantity}}\",\"{{amount}}\"]],\"style\":{\"align\":\"left\"}},{\"id\":\"line_3\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"table_title\",\"type\":\"text\",\"text\":\"\\u684c\\u53f0\\u6c47\\u603b\",\"style\":{\"align\":\"center\",\"bold\":true,\"fontSize\":14}},{\"id\":\"table_1\",\"type\":\"text\",\"text\":\"\\u7ffb\\u53f0\\u7387\\uff1a{{table_turnover}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"table_2\",\"type\":\"text\",\"text\":\"\\u5e73\\u5747\\u6d88\\u8d39\\uff1a\\uffe5{{avg_consumption}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"line_4\",\"type\":\"line\",\"text\":\"\",\"style\":{\"align\":\"center\"}},{\"id\":\"footer_1\",\"type\":\"text\",\"text\":\"\\u6253\\u5370\\u65f6\\u95f4\\uff1a{{print_time}}\",\"style\":{\"align\":\"left\"}},{\"id\":\"footer_2\",\"type\":\"text\",\"text\":\"\\u64cd\\u4f5c\\u5458\\uff1a{{operator}}\",\"style\":{\"align\":\"left\"}}]"', '80mm', 10, 1, 1, 4, '日结营业报表模板，包含营业数据汇总', '2025-08-02 12:57:05', '2025-08-02 12:57:05') [0.62 ms]
 [] []
[2025-08-02 12:57:05] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.80086ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.28 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [1.1 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.55 ms]
 [] []
[2025-08-02 13:00:48] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.27084ms] [webman/log]
 [] []
[2025-08-02 13:00:49] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.15997ms] [webman/log]
 [] []
[2025-08-02 13:00:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.19502ms] [webman/log]
 [] []
[2025-08-02 13:00:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.10609ms] [webman/log]
 [] []
[2025-08-02 13:01:16] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18095ms] [webman/log]
 [] []
[2025-08-02 13:04:14] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [0.06985ms] [webman/log]
 [] []
[2025-08-02 13:04:14] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.90291ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.17 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.99 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-02 13:04:16] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.33307ms] [webman/log]
 [] []
[2025-08-02 13:04:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.27012ms] [webman/log]
 [] []
[2025-08-02 13:04:20] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.02600ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.25 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.77 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.55 ms]
 [] []
[2025-08-02 13:04:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.30684ms] [webman/log]
 [] []
[2025-08-02 13:04:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18692ms] [webman/log]
 [] []
[2025-08-02 13:13:16] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.08895ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.23 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.76 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.68 ms]
 [] []
[2025-08-02 13:13:27] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/addPost [0.05412ms] [webman/log]
 [] []
[2025-08-02 13:13:27] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/addPost [56.1270ms] [webman/log]
[POST]	array (
  'role_name' => '测试',
  'description' => '',
  'permissions' => 
  array (
  ),
  'status' => 1,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '测试' and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] insert into `cs_company_admin_roles` (`role_name`, `company_id`, `updated_at`, `created_at`) values ('测试', '18', '2025-08-02 13:13:27', '2025-08-02 13:13:27') [52.79 ms]
 [] []
[2025-08-02 13:13:27] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [0.02908ms] [webman/log]
 [] []
[2025-08-02 13:13:27] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.57198ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.72 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.53 ms]
 [] []
[2025-08-02 13:13:40] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/addPost [0.04100ms] [webman/log]
 [] []
[2025-08-02 13:13:40] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/addPost [64.2640ms] [webman/log]
[POST]	array (
  'role_name' => '财务',
  'description' => '',
  'permissions' => 
  array (
  ),
  'status' => 1,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务' and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.7 ms]
[SQL]	[connection:mysql] insert into `cs_company_admin_roles` (`role_name`, `company_id`, `updated_at`, `created_at`) values ('财务', '18', '2025-08-02 13:13:40', '2025-08-02 13:13:40') [61.86 ms]
 [] []
[2025-08-02 13:13:40] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [0.03981ms] [webman/log]
 [] []
[2025-08-02 13:13:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.10604ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.12 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.71 ms]
 [] []
[2025-08-02 13:14:05] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [0.04100ms] [webman/log]
 [] []
[2025-08-02 13:14:05] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.52597ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.71 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.9 ms]
 [] []
[2025-08-02 13:14:09] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [0.03099ms] [webman/log]
 [] []
[2025-08-02 13:14:10] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [41.9211ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.78 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.54 ms]
[SQL]	[connection:mysql] update `cs_company_admin_roles` set `role_name` = '财务0', `cs_company_admin_roles`.`updated_at` = '2025-08-02 13:14:09' where `id` = 5 [38.13 ms]
 [] []
[2025-08-02 13:14:10] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.98604ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.26 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.95 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.94 ms]
 [] []
[2025-08-02 13:14:12] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.52382ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.69 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.61 ms]
 [] []
[2025-08-02 13:14:14] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/edit?id=28 [0.04386ms] [webman/log]
 [] []
[2025-08-02 13:14:14] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/edit?id=28 [2.80499ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = '28' and `cs_shops`.`deleted_at` is null limit 1 [0.83 ms]
 [] []
[2025-08-02 13:14:15] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/editPost [0.03480ms] [webman/log]
 [] []
[2025-08-02 13:14:15] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsShop/editPost [5.72991ms] [webman/log]
[POST]	array (
  'id' => 28,
  'shop_name' => '松间茗坊',
  'nickname' => '张建文',
  'mobile' => '***********',
  'password' => '111111',
  'address' => '江西省九江市浔阳西路38号',
  'service_phone' => '0791-1234567',
  'status' => 1,
  'remark' => '化用 “明月松间照”，松的苍劲与茶的温润相衬，自带山林清气',
  'business_hours' => 
  array (
    'monday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'tuesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'wednesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'thursday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'friday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'saturday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'sunday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.75 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.47 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where (`shop_id` = 28 and `is_origin` = 1) and `cs_shop_admins`.`deleted_at` is null limit 1 [0.96 ms]
[SQL]	[connection:mysql] update `cs_shop_admins` set `status` = 1, `is_origin` = 0, `cs_shop_admins`.`updated_at` = '2025-08-02 13:14:15' where (`shop_id` = 28 and `status` = 0 and `is_origin` = 2) and `cs_shop_admins`.`deleted_at` is null [0.54 ms]
 [] []
[2025-08-02 13:14:15] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [4.05907ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.76 ms]
 [] []
[2025-08-02 13:14:18] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.86500ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.83 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-02 13:15:45] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.20696ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.63 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.73 ms]
 [] []
[2025-08-02 13:15:47] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [3.15594ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.78 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.51 ms]
 [] []
[2025-08-02 13:15:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.30805ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.61 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.55 ms]
 [] []
[2025-08-02 13:16:03] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/del [0.03886ms] [webman/log]
 [] []
[2025-08-02 13:16:03] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/del [83.3568ms] [webman/log]
[POST]	array (
  'id' => 4,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `id` in (4) and `cs_company_admin_roles`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] update `cs_company_admin_roles` set `deleted_at` = '2025-08-02 13:16:03', `cs_company_admin_roles`.`updated_at` = '2025-08-02 13:16:03' where `id` = 4 [80.87 ms]
 [] []
[2025-08-02 13:16:03] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.48989ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.26 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.24 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.93 ms]
 [] []
[2025-08-02 13:16:07] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.54194ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.68 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.72 ms]
 [] []
[2025-08-02 13:16:13] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/edit?id=28 [2.41589ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = '28' and `cs_shops`.`deleted_at` is null limit 1 [0.74 ms]
 [] []
[2025-08-02 13:16:14] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsShop/editPost [4.59909ms] [webman/log]
[POST]	array (
  'id' => 28,
  'shop_name' => '松间茗坊',
  'nickname' => '张建文',
  'mobile' => '***********',
  'password' => '111111',
  'address' => '江西省九江市浔阳西路38号',
  'service_phone' => '0791-1234567',
  'status' => 1,
  'remark' => '化用 “明月松间照”，松的苍劲与茶的温润相衬，自带山林清气',
  'business_hours' => 
  array (
    'monday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'tuesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'wednesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'thursday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'friday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'saturday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'sunday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.68 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.42 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where (`shop_id` = 28 and `is_origin` = 1) and `cs_shop_admins`.`deleted_at` is null limit 1 [0.53 ms]
[SQL]	[connection:mysql] update `cs_shop_admins` set `status` = 1, `is_origin` = 0, `cs_shop_admins`.`updated_at` = '2025-08-02 13:16:14' where (`shop_id` = 28 and `status` = 0 and `is_origin` = 2) and `cs_shop_admins`.`deleted_at` is null [0.49 ms]
 [] []
[2025-08-02 13:16:14] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [2.80094ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.59 ms]
 [] []
[2025-08-02 13:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/edit?id=28 [2.54487ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = '28' and `cs_shops`.`deleted_at` is null limit 1 [0.68 ms]
 [] []
[2025-08-02 13:16:18] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsShop/editPost [6.15906ms] [webman/log]
[POST]	array (
  'id' => 28,
  'shop_name' => '松间茗坊',
  'nickname' => '张建文',
  'mobile' => '***********',
  'password' => '111111',
  'address' => '江西省九江市浔阳西路38号',
  'service_phone' => '0791-1234567',
  'status' => 1,
  'remark' => '化用 “明月松间照”，松的苍劲与茶的温润相衬，自带山林清气',
  'business_hours' => 
  array (
    'monday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'tuesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'wednesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'thursday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'friday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'saturday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'sunday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.83 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.59 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where (`shop_id` = 28 and `is_origin` = 1) and `cs_shop_admins`.`deleted_at` is null limit 1 [0.62 ms]
[SQL]	[connection:mysql] update `cs_shop_admins` set `status` = 1, `is_origin` = 0, `cs_shop_admins`.`updated_at` = '2025-08-02 13:16:18' where (`shop_id` = 28 and `status` = 0 and `is_origin` = 2) and `cs_shop_admins`.`deleted_at` is null [0.57 ms]
 [] []
[2025-08-02 13:16:18] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.03792ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.63 ms]
 [] []
[2025-08-02 13:16:55] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.00400ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.88 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.63 ms]
 [] []
[2025-08-02 13:16:57] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.37100ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.7 ms]
 [] []
[2025-08-02 13:16:58] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [2.82907ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.47 ms]
 [] []
[2025-08-02 13:16:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.27205ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.3 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.7 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:17:53] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.63612ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.67 ms]
 [] []
[2025-08-02 13:17:55] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [2.64310ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.08 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.47 ms]
 [] []
[2025-08-02 13:17:55] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [2.69913ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.6 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:18:03] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.00407ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.08 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.62 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.67 ms]
 [] []
[2025-08-02 13:18:04] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [2.69794ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.41 ms]
 [] []
[2025-08-02 13:18:04] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [2.63404ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.52 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.53 ms]
 [] []
[2025-08-02 13:18:09] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [4.04310ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.78 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.87 ms]
 [] []
[2025-08-02 13:18:10] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [3.23081ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.83 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.54 ms]
 [] []
[2025-08-02 13:18:10] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.26108ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.46 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.63 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.57 ms]
 [] []
[2025-08-02 13:20:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.65710ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.81 ms]
 [] []
[2025-08-02 13:20:31] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [2.56705ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.08 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.41 ms]
 [] []
[2025-08-02 13:20:31] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.18002ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.64 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.51 ms]
 [] []
[2025-08-02 13:20:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.09014ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.63 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.66 ms]
 [] []
[2025-08-02 13:20:36] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [2.84719ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务0',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务0' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.47 ms]
 [] []
[2025-08-02 13:20:36] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [2.96401ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.59 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:24:21] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.35185ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.05 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.69 ms]
 [] []
[2025-08-02 13:24:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [4.20784ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.98 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [1.01 ms]
 [] []
[2025-08-02 13:24:25] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/edit?id=28 [2.65598ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.23 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = '28' and `cs_shops`.`deleted_at` is null limit 1 [0.79 ms]
 [] []
[2025-08-02 13:24:27] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsShop/editPost [4.97794ms] [webman/log]
[POST]	array (
  'id' => 28,
  'shop_name' => '松间茗坊',
  'nickname' => '张建文',
  'mobile' => '***********',
  'password' => '111111',
  'address' => '江西省九江市浔阳西路38号',
  'service_phone' => '0791-1234567',
  'status' => 1,
  'remark' => '化用 “明月松间照”，松的苍劲与茶的温润相衬，自带山林清气',
  'business_hours' => 
  array (
    'monday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'tuesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'wednesday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'thursday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'friday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'saturday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
    'sunday' => 
    array (
      'is_open' => true,
      'open' => '09:00',
      'close' => '22:00',
    ),
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.67 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `cs_shops`.`id` = 28 and `cs_shops`.`deleted_at` is null limit 1 [0.42 ms]
[SQL]	[connection:mysql] select * from `cs_shop_admins` where (`shop_id` = 28 and `is_origin` = 1) and `cs_shop_admins`.`deleted_at` is null limit 1 [0.56 ms]
[SQL]	[connection:mysql] update `cs_shop_admins` set `status` = 1, `is_origin` = 0, `cs_shop_admins`.`updated_at` = '2025-08-02 13:24:27' where (`shop_id` = 28 and `status` = 0 and `is_origin` = 2) and `cs_shop_admins`.`deleted_at` is null [0.51 ms]
 [] []
[2025-08-02 13:24:27] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [2.43401ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.49 ms]
 [] []
[2025-08-02 13:24:41] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.88407ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.14 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.58 ms]
 [] []
[2025-08-02 13:24:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.66997ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.79 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.75 ms]
 [] []
[2025-08-02 13:24:49] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdminRole/editPost [62.7219ms] [webman/log]
[POST]	array (
  'id' => 5,
  'role_name' => '财务',
  'description' => '',
  'permissions' => 
  array (
  ),
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`role_name` = '财务' and `company_id` = '18' and `id` <> 5) and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.9 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.48 ms]
[SQL]	[connection:mysql] update `cs_company_admin_roles` set `role_name` = '财务', `cs_company_admin_roles`.`updated_at` = '2025-08-02 13:24:49' where `id` = 5 [59.26 ms]
 [] []
[2025-08-02 13:24:49] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.46081ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.95 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.96 ms]
 [] []
[2025-08-02 13:25:49] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.05102ms] [webman/log]
 [] []
[2025-08-02 13:25:49] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.19812ms] [webman/log]
 [] []
[2025-08-02 13:26:26] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.54695ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.73 ms]
 [] []
[2025-08-02 13:26:34] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [0.04076ms] [webman/log]
 [] []
[2025-08-02 13:26:34] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [3.25512ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.69 ms]
 [] []
[2025-08-02 13:32:04] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.06222ms] [webman/log]
 [] []
[2025-08-02 13:32:04] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.16689ms] [webman/log]
 [] []
[2025-08-02 13:32:04] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.23508ms] [webman/log]
 [] []
[2025-08-02 13:32:15] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.38409ms] [webman/log]
 [] []
[2025-08-02 13:32:15] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18191ms] [webman/log]
 [] []
[2025-08-02 13:35:24] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.05483ms] [webman/log]
 [] []
[2025-08-02 13:35:24] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.03004ms] [webman/log]
 [] []
[2025-08-02 13:35:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.42719ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.98 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.4 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.45 ms]
 [] []
[2025-08-02 13:35:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.68108ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.55 ms]
 [] []
[2025-08-02 13:35:27] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.19288ms] [webman/log]
 [] []
[2025-08-02 13:35:28] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.49209ms] [webman/log]
 [] []
[2025-08-02 13:35:31] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03790ms] [webman/log]
 [] []
[2025-08-02 13:35:31] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.01692ms] [webman/log]
 [] []
[2025-08-02 13:35:31] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.30917ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.81 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.46 ms]
 [] []
[2025-08-02 13:35:31] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.57713ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:35:34] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.41604ms] [webman/log]
 [] []
[2025-08-02 13:35:34] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.18692ms] [webman/log]
 [] []
[2025-08-02 13:36:47] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03290ms] [webman/log]
 [] []
[2025-08-02 13:36:47] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.02098ms] [webman/log]
 [] []
[2025-08-02 13:36:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.54497ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.93 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.62 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.65 ms]
 [] []
[2025-08-02 13:36:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.77001ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.83 ms]
 [] []
[2025-08-02 13:38:11] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03600ms] [webman/log]
 [] []
[2025-08-02 13:38:11] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.01883ms] [webman/log]
 [] []
[2025-08-02 13:38:11] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.84085ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.96 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.74 ms]
 [] []
[2025-08-02 13:38:11] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.86991ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.78 ms]
 [] []
[2025-08-02 13:38:33] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03194ms] [webman/log]
 [] []
[2025-08-02 13:38:33] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.01502ms] [webman/log]
 [] []
[2025-08-02 13:38:33] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.09817ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.74 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.46 ms]
 [] []
[2025-08-02 13:38:33] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.95097ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.58 ms]
 [] []
[2025-08-02 13:39:44] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/addPost [0.04100ms] [webman/log]
 [] []
[2025-08-02 13:39:44] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/addPost [73.6680ms] [webman/log]
[POST]	array (
  'id' => NULL,
  'nickname' => '小张',
  'username' => '13340010001',
  'password' => '111111',
  'avatar' => '',
  'role_id' => 5,
  'status' => 1,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '13340010001') and `cs_company_admins`.`deleted_at` is null limit 1 [0.81 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.55 ms]
[SQL]	[connection:mysql] insert into `cs_company_admins` (`nickname`, `username`, `password`, `avatar`, `role_id`, `status`, `company_id`, `updated_at`, `created_at`) values ('小张', '13340010001', '111111', '', 5, 1, '18', '2025-08-02 13:39:44', '2025-08-02 13:39:44') [69.15 ms]
 [] []
[2025-08-02 13:39:44] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [6.92987ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.76 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.98 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.92 ms]
 [] []
[2025-08-02 13:39:54] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03504ms] [webman/log]
 [] []
[2025-08-02 13:39:54] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.02789ms] [webman/log]
 [] []
[2025-08-02 13:39:54] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.93502ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.98 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.77 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.63 ms]
 [] []
[2025-08-02 13:39:54] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.71399ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.72 ms]
 [] []
[2025-08-02 13:41:09] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [0.03004ms] [webman/log]
 [] []
[2025-08-02 13:41:09] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [1.98101ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.58 ms]
 [] []
[2025-08-02 13:41:17] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/editPost [0.04100ms] [webman/log]
 [] []
[2025-08-02 13:41:17] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/editPost [3.21388ms] [webman/log]
[POST]	array (
  'id' => 19,
  'nickname' => '小张',
  'username' => '13340010001',
  'avatar' => '',
  'role_id' => 5,
  'status' => 1,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '13340010001' and `id` <> 19) and `cs_company_admins`.`deleted_at` is null limit 1 [0.86 ms]
 [] []
[2025-08-02 13:41:26] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=18 [0.04506ms] [webman/log]
 [] []
[2025-08-02 13:41:26] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=18 [2.70891ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 18 and `cs_company_admins`.`deleted_at` is null limit 1 [0.83 ms]
 [] []
[2025-08-02 13:41:30] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [0.04696ms] [webman/log]
 [] []
[2025-08-02 13:41:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [2.10785ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.64 ms]
 [] []
[2025-08-02 13:41:54] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [0.05888ms] [webman/log]
 [] []
[2025-08-02 13:41:54] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [2.21800ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.66 ms]
 [] []
[2025-08-02 13:44:36] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03600ms] [webman/log]
 [] []
[2025-08-02 13:44:36] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.05102ms] [webman/log]
 [] []
[2025-08-02 13:44:36] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.47106ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.68 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.46 ms]
 [] []
[2025-08-02 13:44:36] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.62696ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.5 ms]
 [] []
[2025-08-02 13:44:39] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.17499ms] [webman/log]
 [] []
[2025-08-02 13:44:39] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.16999ms] [webman/log]
 [] []
[2025-08-02 13:44:52] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [0.03814ms] [webman/log]
 [] []
[2025-08-02 13:44:52] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [2.15792ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.61 ms]
 [] []
[2025-08-02 13:47:06] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03600ms] [webman/log]
 [] []
[2025-08-02 13:47:06] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.02288ms] [webman/log]
 [] []
[2025-08-02 13:47:06] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [7.16114ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.88 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.98 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.63 ms]
 [] []
[2025-08-02 13:47:06] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [2.14099ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.16 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.75 ms]
 [] []
[2025-08-02 13:47:22] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [0.03504ms] [webman/log]
 [] []
[2025-08-02 13:47:22] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/edit?id=19 [2.26211ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.71 ms]
 [] []
[2025-08-02 13:47:25] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/editPost [0.04816ms] [webman/log]
 [] []
[2025-08-02 13:47:25] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/editPost [4.17304ms] [webman/log]
[POST]	array (
  'id' => 19,
  'nickname' => '小张',
  'username' => '13340010001',
  'password' => '111111',
  'avatar' => '',
  'role_id' => 5,
  'status' => 1,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '13340010001' and `id` <> 19) and `cs_company_admins`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.47 ms]
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.46 ms]
 [] []
[2025-08-02 13:47:25] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03099ms] [webman/log]
 [] []
[2025-08-02 13:47:25] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.64296ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.73 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.54 ms]
 [] []
[2025-08-02 13:47:30] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/state [0.03910ms] [webman/log]
 [] []
[2025-08-02 13:47:30] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/state [88.1719ms] [webman/log]
[POST]	array (
  'id' => 19,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.74 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `status` = 0, `cs_company_admins`.`updated_at` = '2025-08-02 13:47:30' where `id` = 19 [85.01 ms]
 [] []
[2025-08-02 13:47:30] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.05006ms] [webman/log]
 [] []
[2025-08-02 13:47:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [5.67197ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.23 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.77 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.71 ms]
 [] []
[2025-08-02 13:47:33] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/state [0.04196ms] [webman/log]
 [] []
[2025-08-02 13:47:33] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/state [19.3281ms] [webman/log]
[POST]	array (
  'id' => 19,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `status` = 1, `cs_company_admins`.`updated_at` = '2025-08-02 13:47:33' where `id` = 19 [16.65 ms]
 [] []
[2025-08-02 13:47:33] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.02908ms] [webman/log]
 [] []
[2025-08-02 13:47:33] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.25601ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.64 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.48 ms]
 [] []
[2025-08-02 13:48:49] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/del [0.04100ms] [webman/log]
 [] []
[2025-08-02 13:48:49] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/del [60.8820ms] [webman/log]
[POST]	array (
  'id' => 19,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 19 and `cs_company_admins`.`deleted_at` is null limit 1 [0.74 ms]
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` in (19) and `cs_company_admins`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `deleted_at` = '2025-08-02 13:48:49', `cs_company_admins`.`updated_at` = '2025-08-02 13:48:49' where `id` = 19 [57.49 ms]
 [] []
[2025-08-02 13:48:49] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03290ms] [webman/log]
 [] []
[2025-08-02 13:48:49] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [5.27095ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.2 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.16 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.14 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-02 13:49:04] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/state [0.04506ms] [webman/log]
 [] []
[2025-08-02 13:49:04] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/state [58.3968ms] [webman/log]
[POST]	array (
  'id' => 18,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 18 and `cs_company_admins`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `status` = 0, `cs_company_admins`.`updated_at` = '2025-08-02 13:49:04' where `id` = 18 [55.75 ms]
 [] []
[2025-08-02 13:49:04] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.02694ms] [webman/log]
 [] []
[2025-08-02 13:49:04] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.08101ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.67 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:49:07] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/state [0.03600ms] [webman/log]
 [] []
[2025-08-02 13:49:07] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/CsCompanyAdmin/state [11.3258ms] [webman/log]
[POST]	array (
  'id' => 18,
)
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = 18 and `cs_company_admins`.`deleted_at` is null limit 1 [0.63 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `status` = 1, `cs_company_admins`.`updated_at` = '2025-08-02 13:49:07' where `id` = 18 [8.97 ms]
 [] []
[2025-08-02 13:49:07] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [0.03194ms] [webman/log]
 [] []
[2025-08-02 13:49:07] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.28390ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.77 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-02 13:49:21] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.64200ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.28 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.2 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.19 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.94 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.67 ms]
 [] []
[2025-08-02 13:49:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [5.53202ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.75 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.73 ms]
 [] []
[2025-08-02 13:49:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.59382ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:49:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.29709ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.75 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.6 ms]
 [] []
[2025-08-02 13:49:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.17208ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.75 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:49:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.73997ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-02 13:49:25] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.26514ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.76 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.54 ms]
 [] []
[2025-08-02 13:49:26] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [4.26197ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.76 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.6 ms]
 [] []
[2025-08-02 13:49:26] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.71208ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-02 13:49:26] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.29804ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:574874ffd8b53a149d05548ea506cfb9') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:574874ffd8b53a149d05548ea506cfb9') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.75 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.61 ms]
 [] []
[2025-08-02 17:53:32] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/login [50.7700ms] [webman/log]
 [] []
[2025-08-02 17:53:32] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [104.325ms] [webman/log]
[POST]	array (
  'username' => '19815092140',
  'password' => '96e79218965eb72c92a549dd5a330112',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '19815092140') and `cs_company_admins`.`deleted_at` is null limit 1 [24.93 ms]
 [] []
[2025-08-02 17:53:37] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [382.836ms] [webman/log]
[POST]	array (
  'username' => '19815092140',
  'password' => 'e3ceb5881a0a1fdaad01296d7554868d',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '19815092140') and `cs_company_admins`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` = 13 and `cs_companys`.`deleted_at` is null limit 1 [6.6 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `login_time` = '2025-08-02 17:53:37', `cs_company_admins`.`updated_at` = '2025-08-02 17:53:37' where `id` = 13 [277.28 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 1 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [7.07 ms]
[SQL]	[connection:mysql] select `id` from `cs_menus` where (`menu_type` = 1 and `status` = 1) and `cs_menus`.`deleted_at` is null [5.21 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `menu_img`, `menu_sign`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and 0 = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.53 ms]
[Redis]	[connection:default] ...
 [] []
