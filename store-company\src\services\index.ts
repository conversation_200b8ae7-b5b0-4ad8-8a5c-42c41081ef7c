// 导出基础API服务
export { default as service } from './api'

// 从 api.ts 导出类型和其他函数（除了 login）
export type { LoginResponse } from './api'
export { BASE_URL, UPLOAD_URL, upload } from './api'

// 导出认证相关API（使用 authApi 中的 login 作为主要的登录函数）
export * from './authApi'

// 导出基础API工具
export * from './baseApi'

// 导出管理员相关API
export * from './adminApi'

// 导出角色相关API
export * from './roleApi'

// 导出门店相关API
export * from './shopApi'

// 导出仪表板相关API
export * from './dashboardApi'

// 如果需要使用 api.ts 中的 login，可以重命名导出
export { login as apiLogin } from './api'
