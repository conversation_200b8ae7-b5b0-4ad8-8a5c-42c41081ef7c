<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * cs_order_goodss 订单商品表
 * @property integer $id (主键)
 * @property integer $shop_id 所属门店
 * @property integer $order_id 所属订单
 * @property integer $goods_id 商品
 * @property integer $goods_type 商品类型 0：普通商品；1：套餐商品；2：配品，3：套餐内商品
 * @property string $goods_name 商品名称
 * @property string $goods_price 商品单价
 * @property integer $goods_nums 商品数量
 * @property string $cost_price 成本价
 * @property mixed $specification_item_list 规格项
 * @property string $selected_labels 已选标签
 * @property string $item_key 选项的key，用户附属产品定位
 * @property integer $parent_id 父ID
 * @property integer $goods_status 商品状态
 * @property string $remark 备注
 * @property string $created_at 添加时间
 * @property string $updated_at 修改时间
 * @property string $deleted_at 删除时间
 */
class CsOrderGoods extends ShopBaseModel
{
    use SoftDeletes;

    protected $table = 'cs_order_goodss';

    /**
     * 验证字段
     * @var string[]
     */
    public static array $validateFields = ['id', 'shop_id', 'order_id', 'goods_id', 'goods_type', 'goods_name', 'goods_price', 'goods_nums', 'cost_price', 'specification_item_list', 'selected_labels', 'item_key', 'parent_id', 'goods_status', 'remark', ];
}
