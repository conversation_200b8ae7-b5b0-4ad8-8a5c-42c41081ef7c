-- Dashboard测试数据插入脚本
-- 注意：请根据实际情况修改company_id和shop_id

-- 1. 插入测试企业（如果不存在）
INSERT IGNORE INTO `cs_companys` (`id`, `company_name`, `contact_person`, `contact_phone`, `status`, `created_at`, `updated_at`) 
VALUES (1, '测试企业', '张三', '13800138000', 1, NOW(), NOW());

-- 2. 插入测试门店（如果不存在）
INSERT IGNORE INTO `cs_shops` (`id`, `company_id`, `shop_name`, `status`, `created_at`, `updated_at`) 
VALUES 
(1, 1, '旗舰店', 1, NOW(), NOW()),
(2, 1, '商场店', 1, NOW(), NOW()),
(3, 1, '社区店', 1, NOW(), NOW());

-- 3. 插入支付方式
INSERT IGNORE INTO `cs_payment_methods` (`id`, `shop_id`, `payment_method_name`, `status`, `sort`, `created_at`, `updated_at`) 
VALUES 
(1, 1, '微信支付', 1, 1, NOW(), NOW()),
(2, 1, '支付宝', 1, 2, NOW(), NOW()),
(3, 1, '现金支付', 1, 3, NOW(), NOW()),
(4, 1, '银行卡', 1, 4, NOW(), NOW()),
(5, 2, '微信支付', 1, 1, NOW(), NOW()),
(6, 2, '支付宝', 1, 2, NOW(), NOW()),
(7, 3, '微信支付', 1, 1, NOW(), NOW()),
(8, 3, '现金支付', 1, 2, NOW(), NOW());

-- 4. 插入测试商品
INSERT IGNORE INTO `cs_goods` (`id`, `shop_id`, `goods_name`, `goods_price`, `status`, `created_at`, `updated_at`) 
VALUES 
(1, 1, '招牌奶茶', 30.00, 1, NOW(), NOW()),
(2, 1, '珍珠奶茶', 28.00, 1, NOW(), NOW()),
(3, 1, '布丁奶茶', 32.00, 1, NOW(), NOW()),
(4, 2, '招牌奶茶', 30.00, 1, NOW(), NOW()),
(5, 2, '红豆奶茶', 29.00, 1, NOW(), NOW());

-- 5. 插入今日测试订单
INSERT INTO `cs_orders` (
    `shop_id`, `order_sn`, `order_status`, `user_id`, `goods_money`, `real_pay_money`, 
    `payment_type`, `pay_time`, `created_at`, `updated_at`
) VALUES 
-- 旗舰店订单
(1, 'ORD' + UNIX_TIMESTAMP() + '001', 2, 1, 90.00, 90.00, 1, NOW(), NOW(), NOW()),
(1, 'ORD' + UNIX_TIMESTAMP() + '002', 2, 2, 60.00, 60.00, 2, NOW(), NOW(), NOW()),
(1, 'ORD' + UNIX_TIMESTAMP() + '003', 2, 3, 120.00, 120.00, 1, NOW(), NOW(), NOW()),
(1, 'ORD' + UNIX_TIMESTAMP() + '004', 2, 4, 45.00, 45.00, 3, NOW(), NOW(), NOW()),
(1, 'ORD' + UNIX_TIMESTAMP() + '005', 2, 5, 75.00, 75.00, 4, NOW(), NOW(), NOW()),

-- 商场店订单
(2, 'ORD' + UNIX_TIMESTAMP() + '006', 2, 6, 80.00, 80.00, 5, NOW(), NOW(), NOW()),
(2, 'ORD' + UNIX_TIMESTAMP() + '007', 2, 7, 95.00, 95.00, 6, NOW(), NOW(), NOW()),
(2, 'ORD' + UNIX_TIMESTAMP() + '008', 2, 8, 110.00, 110.00, 5, NOW(), NOW(), NOW()),

-- 社区店订单
(3, 'ORD' + UNIX_TIMESTAMP() + '009', 2, 9, 65.00, 65.00, 7, NOW(), NOW(), NOW()),
(3, 'ORD' + UNIX_TIMESTAMP() + '010', 2, 10, 85.00, 85.00, 8, NOW(), NOW(), NOW());

-- 6. 插入订单商品明细
INSERT INTO `cs_order_goodss` (
    `shop_id`, `order_id`, `goods_id`, `goods_name`, `goods_price`, `goods_nums`, `created_at`, `updated_at`
) VALUES 
-- 对应上面的订单
(1, LAST_INSERT_ID()-9, 1, '招牌奶茶', 30.00, 3, NOW(), NOW()),
(1, LAST_INSERT_ID()-8, 2, '珍珠奶茶', 28.00, 2, NOW(), NOW()),
(1, LAST_INSERT_ID()-7, 3, '布丁奶茶', 32.00, 3, NOW(), NOW()),
(1, LAST_INSERT_ID()-6, 1, '招牌奶茶', 30.00, 1, NOW(), NOW()),
(1, LAST_INSERT_ID()-6, 2, '珍珠奶茶', 28.00, 1, NOW(), NOW()),
(1, LAST_INSERT_ID()-5, 3, '布丁奶茶', 32.00, 2, NOW(), NOW()),

(2, LAST_INSERT_ID()-4, 4, '招牌奶茶', 30.00, 2, NOW(), NOW()),
(2, LAST_INSERT_ID()-4, 5, '红豆奶茶', 29.00, 1, NOW(), NOW()),
(2, LAST_INSERT_ID()-3, 4, '招牌奶茶', 30.00, 3, NOW(), NOW()),
(2, LAST_INSERT_ID()-2, 5, '红豆奶茶', 29.00, 3, NOW(), NOW()),

(3, LAST_INSERT_ID()-1, 1, '招牌奶茶', 30.00, 2, NOW(), NOW()),
(3, LAST_INSERT_ID(), 2, '珍珠奶茶', 28.00, 3, NOW(), NOW());

-- 7. 插入库存数据
INSERT IGNORE INTO `cs_inventory` (`shop_id`, `goods_id`, `current_stock`, `min_stock`, `created_at`, `updated_at`) 
VALUES 
(1, 1, 50, 10, NOW(), NOW()),
(1, 2, 8, 10, NOW(), NOW()),  -- 低库存
(1, 3, 30, 10, NOW(), NOW()),
(2, 4, 25, 10, NOW(), NOW()),
(2, 5, 5, 10, NOW(), NOW()),  -- 低库存
(3, 1, 15, 10, NOW(), NOW());

-- 8. 如果已经添加了company_id字段，更新这些测试数据
-- UPDATE `cs_orders` SET `company_id` = 1 WHERE `shop_id` IN (1, 2, 3);
-- UPDATE `cs_order_goodss` SET `company_id` = 1 WHERE `shop_id` IN (1, 2, 3);

SELECT '测试数据插入完成！' as message;
