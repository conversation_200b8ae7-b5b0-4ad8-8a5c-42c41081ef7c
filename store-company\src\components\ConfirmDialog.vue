<template>
  <div v-if="visible" class="confirm-overlay" @click.self="handleCancel">
    <div class="confirm-dialog" :class="typeClass">
      <div class="confirm-header">
        <div class="confirm-icon">
          <i :class="iconClass"></i>
        </div>
        <h3 class="confirm-title">{{ title }}</h3>
      </div>
      
      <div class="confirm-body">
        <p class="confirm-message">{{ message }}</p>
        <div v-if="details" class="confirm-details">
          {{ details }}
        </div>
      </div>
      
      <div class="confirm-footer">
        <button 
          class="btn btn-secondary" 
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </button>
        <button 
          class="btn" 
          :class="confirmButtonClass"
          @click="handleConfirm"
          :disabled="loading"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? loadingText : confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface ConfirmDialogProps {
  visible: boolean
  type?: 'info' | 'warning' | 'danger' | 'success'
  title?: string
  message: string
  details?: string
  confirmText?: string
  cancelText?: string
  loadingText?: string
  loading?: boolean
}

const props = withDefaults(defineProps<ConfirmDialogProps>(), {
  type: 'warning',
  title: '确认操作',
  confirmText: '确定',
  cancelText: '取消',
  loadingText: '处理中...',
  loading: false
})

const emit = defineEmits<{
  confirm: []
  cancel: []
}>()

const typeClass = computed(() => `confirm-dialog--${props.type}`)

const iconClass = computed(() => {
  const iconMap = {
    info: '📋',
    warning: '⚠️',
    danger: '🗑️',
    success: '✅'
  }
  return iconMap[props.type]
})

const confirmButtonClass = computed(() => {
  const classMap = {
    info: 'btn-primary',
    warning: 'btn-warning',
    danger: 'btn-danger',
    success: 'btn-success'
  }
  return classMap[props.type]
})

const handleConfirm = () => {
  if (!props.loading) {
    emit('confirm')
  }
}

const handleCancel = () => {
  if (!props.loading) {
    emit('cancel')
  }
}
</script>

<style scoped>
/* 确认对话框样式 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.25);
  width: 90%;
  max-width: 480px;
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.confirm-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f2f5;
}

.confirm-icon {
  font-size: 48px;
  margin-bottom: 16px;
  line-height: 1;
}

.confirm-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.confirm-dialog--danger .confirm-title {
  color: #dc2626;
}

.confirm-dialog--warning .confirm-title {
  color: #d97706;
}

.confirm-dialog--success .confirm-title {
  color: #059669;
}

.confirm-body {
  padding: 16px 24px 24px;
  text-align: center;
}

.confirm-message {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #374151;
  line-height: 1.5;
}

.confirm-details {
  font-size: 14px;
  color: #6b7280;
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #e5e7eb;
}

.confirm-dialog--danger .confirm-details {
  background: #fef2f2;
  border-left-color: #fca5a5;
}

.confirm-dialog--warning .confirm-details {
  background: #fffbeb;
  border-left-color: #fcd34d;
}

.confirm-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-warning {
  background: #f59e0b;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #d97706;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .confirm-dialog {
    width: 95%;
    margin: 16px;
  }
  
  .confirm-header {
    padding: 20px 20px 12px;
  }
  
  .confirm-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .confirm-title {
    font-size: 16px;
  }
  
  .confirm-body {
    padding: 12px 20px 20px;
  }
  
  .confirm-footer {
    padding: 12px 20px 20px;
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
  }
}
</style>
