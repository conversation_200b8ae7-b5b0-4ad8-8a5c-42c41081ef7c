<template>
  <div class="expired-container">
    <div class="expired-card">
      <div class="card-content">
        <!-- 左侧内容区域 -->
        <div class="content-left">
          <h1 class="main-title">您的套餐已到期</h1>
          <div class="subtitle-group">
            <p class="subtitle">为避免影响正常使用，</p>
            <p class="subtitle">请及时联系客服续费套餐</p>
          </div>

          <button class="renew-btn" @click="contactService">
            重新登录
          </button>

          <div class="contact-info">
            <div class="contact-item">
              <div class="contact-icon">📞</div>
              <div class="contact-details">
                <div class="contact-label">客服热线</div>
                <div class="contact-value">{{ customerService.hotline }}</div>
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">💬</div>
              <div class="contact-details">
                <div class="contact-label">微信客服</div>
                <div class="contact-value">{{ customerService.wechat }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧图标区域 -->
        <div class="content-right">
          <div class="megaphone-container">
            <div class="megaphone-icon">
              <div class="megaphone-body"></div>
              <div class="megaphone-handle"></div>
            </div>
            <div class="sound-waves">
              <div class="wave wave-1"></div>
              <div class="wave wave-2"></div>
              <div class="wave wave-3"></div>
            </div>
          </div>

          <!-- 装饰性元素 -->
          <div class="decorative-elements">
            <div class="floating-dot dot-1"></div>
            <div class="floating-dot dot-2"></div>
            <div class="floating-dot dot-3"></div>
            <div class="floating-dot dot-4"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCustomerServiceInfo } from '@/services/authApi'

const router = useRouter()

// 客服信息
const customerService = ref({
  hotline: '************', // 默认值
  wechat: 'Z19815092140' // 默认值
})

// 加载中状态
const loading = ref(false)

// 获取客服信息
const fetchCustomerServiceInfo = async () => {
  try {
    loading.value = true
    const response = await getCustomerServiceInfo()
    if (response.hotline) {
      customerService.value.hotline = response.hotline
    }
    if (response.wechat) {
      customerService.value.wechat = response.wechat
    }
  } catch (error) {
    console.error('获取客服信息失败:', error)
    // 失败时使用默认值，不显示错误信息，避免影响用户体验
  } finally {
    loading.value = false
  }
}

// 联系客服/重新登录
const contactService = () => {
  // 跳转到登录页面，用户可以联系客服或重新登录
  router.push('/login')
}

// 页面加载时获取客服信息
onMounted(() => {
  fetchCustomerServiceInfo()
})
</script>

<style scoped>
/* 主容器 */
.expired-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-deep-blue) 0%, var(--primary-business-blue) 50%, var(--primary-steel-blue) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  font-family: var(--font-family);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.expired-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
    radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%),
    radial-gradient(ellipse at 50% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(0.5deg); }
  66% { transform: translate(-20px, 20px) rotate(-0.5deg); }
}

/* 主卡片 */
.expired-card {
  background: var(--glass-background);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow:
    0 20px 60px var(--shadow-deep),
    0 8px 32px var(--shadow-medium),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  width: 100%;
  max-width: 900px;
  min-height: 500px;
  position: relative;
  z-index: 5;
  animation:
    slideUp 0.8s ease-out,
    cardPulse 4s ease-in-out infinite 2s;
  overflow: hidden;
}

@keyframes cardPulse {
  0%, 100% {
    box-shadow:
      0 20px 60px var(--shadow-deep),
      0 8px 32px var(--shadow-medium),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }
  50% {
    box-shadow:
      0 25px 70px rgba(13, 27, 42, 0.35),
      0 12px 40px rgba(13, 27, 42, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.15) inset;
  }
}

/* 卡片内容布局 */
.card-content {
  display: flex;
  align-items: center;
  min-height: 500px;
  position: relative;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 左侧内容区域 */
.content-left {
  flex: 1;
  padding: 80px 60px 80px 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
}

/* 右侧图标区域 */
.content-right {
  flex: 0 0 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, rgba(232, 184, 109, 0.08) 0%, rgba(184, 197, 209, 0.12) 100%);
}

/* 喇叭容器 */
.megaphone-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 喇叭图标 - 使用CSS绘制 */
.megaphone-icon {
  position: relative;
  animation:
    slideInRight 1s ease-out,
    float 3s ease-in-out infinite 1s;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px) rotate(10deg);
  }
  to {
    opacity: 1;
    transform: translateX(0) rotate(0deg);
  }
}

.megaphone-body {
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
  border-radius: 0 40px 40px 0;
  position: relative;
  box-shadow:
    0 8px 24px rgba(232, 184, 109, 0.3),
    inset 0 2px 8px rgba(255, 255, 255, 0.2);
}

.megaphone-body::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 40px solid transparent;
  border-bottom: 40px solid transparent;
  border-right: 40px solid var(--accent-soft-gold);
}

.megaphone-handle {
  position: absolute;
  bottom: -15px;
  left: 20px;
  width: 30px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-steel-blue) 0%, var(--secondary-steel-blue) 100%);
  border-radius: 15px 15px 8px 8px;
  box-shadow: 0 4px 12px rgba(65, 90, 119, 0.3);
}

/* 声波效果 */
.sound-waves {
  position: absolute;
  right: -60px;
  top: 50%;
  transform: translateY(-50%);
}

.wave {
  position: absolute;
  border-radius: 3px;
  background: linear-gradient(90deg, var(--accent-soft-gold), var(--accent-warm-silver));
  animation: wave-animation 2s ease-in-out infinite;
}

.wave-1 {
  width: 30px;
  height: 6px;
  top: -15px;
  animation-delay: 0s;
}

.wave-2 {
  width: 25px;
  height: 5px;
  top: -2px;
  animation-delay: 0.3s;
}

.wave-3 {
  width: 20px;
  height: 4px;
  top: 12px;
  animation-delay: 0.6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(-2deg);
  }
  50% {
    transform: translateY(-8px) rotate(2deg);
  }
}

@keyframes wave-animation {
  0%, 100% {
    opacity: 0.4;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.3);
  }
}

/* 装饰性浮动元素 */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.floating-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, var(--accent-soft-gold), var(--accent-warm-silver));
  border-radius: 50%;
  opacity: 0.6;
  animation: floatAround 8s ease-in-out infinite;
}

.dot-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
  animation-duration: 6s;
}

.dot-2 {
  top: 60%;
  left: 25%;
  animation-delay: 2s;
  animation-duration: 8s;
}

.dot-3 {
  top: 30%;
  right: 20%;
  animation-delay: 4s;
  animation-duration: 7s;
}

.dot-4 {
  bottom: 25%;
  right: 15%;
  animation-delay: 6s;
  animation-duration: 9s;
}

@keyframes floatAround {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translate(20px, -15px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translate(-10px, -25px) scale(0.8);
    opacity: 0.4;
  }
  75% {
    transform: translate(-20px, 10px) scale(1.1);
    opacity: 0.7;
  }
}

/* 标题样式 */
.main-title {
  font-size: 42px;
  font-weight: 700;
  color: var(--warning-color);
  margin-bottom: 32px;
  line-height: 1.2;
  letter-spacing: -0.5px;
  animation: fadeInLeft 0.8s ease-out;
  position: relative;
}

.main-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: 2px;
  animation: expandWidth 1s ease-out 0.5s forwards;
}

/* 副标题组 */
.subtitle-group {
  margin-bottom: 48px;
}

.subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  margin: 4px 0;
  line-height: 1.6;
  font-weight: 400;
  animation: fadeInLeft 0.8s ease-out;
}

.subtitle:nth-child(1) {
  animation-delay: 0.2s;
}

.subtitle:nth-child(2) {
  animation-delay: 0.4s;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes expandWidth {
  from {
    width: 0;
  }
  to {
    width: 80px;
  }
}

/* 续费按钮 */
.renew-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 28px;
  padding: 18px 48px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 32px;
  box-shadow:
    0 8px 24px rgba(65, 90, 119, 0.25),
    0 4px 12px rgba(65, 90, 119, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 180px;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.renew-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.renew-btn:hover::before {
  left: 100%;
}

.renew-btn:hover {
  transform: translateY(-3px);
  box-shadow:
    0 12px 32px rgba(65, 90, 119, 0.3),
    0 6px 16px rgba(65, 90, 119, 0.2);
}

.renew-btn:active {
  transform: translateY(-1px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 联系信息 */
.contact-info {
  display: flex;
  gap: 32px;
  margin-top: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(65, 90, 119, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  animation: slideInUp 0.6s ease-out;
}

.contact-item:nth-child(1) {
  animation-delay: 0.2s;
}

.contact-item:nth-child(2) {
  animation-delay: 0.4s;
}

.contact-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.8);
  border-color: var(--primary-steel-blue);
  box-shadow: 0 4px 16px var(--shadow-light);
}

.contact-icon {
  font-size: 24px;
  animation: bounce 2s ease-in-out infinite;
}

.contact-item:nth-child(1) .contact-icon {
  animation-delay: 0.5s;
}

.contact-item:nth-child(2) .contact-icon {
  animation-delay: 1s;
}

.contact-details {
  text-align: left;
}

.contact-label {
  font-size: 12px;
  color: var(--text-light);
  margin-bottom: 2px;
  opacity: 0.8;
}

.contact-value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 600;
  white-space: nowrap;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .expired-card {
    max-width: 800px;
  }

  .content-right {
    flex: 0 0 350px;
  }

  .content-left {
    padding: 60px 40px 60px 60px;
  }
}

@media (max-width: 768px) {
  .expired-container {
    padding: 20px;
  }

  .expired-card {
    max-width: 100%;
    min-height: auto;
  }

  .card-content {
    flex-direction: column;
    min-height: auto;
  }

  .content-left {
    padding: 60px 40px 40px;
    text-align: center;
  }

  .content-right {
    flex: none;
    padding: 40px;
    background: linear-gradient(180deg, rgba(232, 184, 109, 0.05) 0%, rgba(184, 197, 209, 0.08) 100%);
  }

  .main-title {
    font-size: 36px;
  }

  .subtitle {
    font-size: 16px;
  }

  .megaphone-body {
    width: 100px;
    height: 65px;
  }

  .sound-waves {
    right: -50px;
  }

  .contact-info {
    flex-direction: column;
    gap: 16px;
  }

  .contact-item {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .expired-container {
    padding: 16px;
  }

  .content-left {
    padding: 40px 24px 24px;
  }

  .content-right {
    padding: 24px;
  }

  .main-title {
    font-size: 28px;
    margin-bottom: 24px;
  }

  .subtitle-group {
    margin-bottom: 36px;
  }

  .subtitle {
    font-size: 15px;
  }

  .renew-btn {
    width: 100%;
    max-width: 280px;
    padding: 16px 36px;
    font-size: 16px;
  }

  .megaphone-body {
    width: 80px;
    height: 50px;
  }

  .megaphone-body::before {
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    border-right: 30px solid #FF8C00;
    left: -15px;
  }

  .megaphone-handle {
    width: 24px;
    height: 32px;
    left: 16px;
    bottom: -12px;
  }

  .sound-waves {
    right: -40px;
  }

  .wave-1 {
    width: 24px;
    height: 5px;
  }

  .wave-2 {
    width: 20px;
    height: 4px;
  }

  .wave-3 {
    width: 16px;
    height: 3px;
  }

  .contact-info {
    gap: 12px;
  }

  .contact-item {
    padding: 12px 16px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .contact-details {
    text-align: center;
  }

  .contact-icon {
    font-size: 20px;
  }
}
</style>
