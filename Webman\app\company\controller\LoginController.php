<?php

namespace app\company\controller;

use app\model\CsAdmin;
use app\model\CsCompany;
use app\model\CsCompanyAdmin;
use app\model\CsSystem;
use app\service\Menu;
use support\Redis;
use support\Request;
use support\Response;
use Webman\Event\Event;

class LoginController
{
    use Menu;

    protected $noNeedLogin = ['login'];

    public function getLoginPage(Request $request): Response
    {
        $system = CsSystem::find(3);
        $backData = [
            'software_version' => $system->software_version,
            'serial_number' => $system->serial_number,
            'copyright_info' => $system->copyright_info,
            'software_name' => $system->software_name,
            'login_background' => get_image_url($system->login_background),
        ];
        return success($backData);
    }

    /**
     * 发送注册短信验证码
     * @param Request $request
     * @return Response
     */
    public function sendRegisterSms(Request $request): Response
    {
        $phone = $request->post('phone', '');
        if (empty($phone)) {
            return fail('手机号不能为空');
        }
        
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            return fail('请输入正确的手机号码');
        }
        
        // 验证手机号是否已注册
        $company = CsCompany::where('mobile', $phone)->first();
        if (!empty($company)) {
            return fail('该手机号已注册，请直接登录');
        }
        
        // 检查是否频繁发送（60秒内只能发送一次）
        $lastSendTime = Redis::get('register_sms_last_send:' . $phone);
        if (!empty($lastSendTime) && (time() - $lastSendTime) < 60) {
            return fail('请稍后再试，验证码发送过于频繁');
        }
        
        // 生成6位随机验证码
        $smsCode = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        // 将验证码存储到Redis，设置5分钟过期
        Redis::setEx('register_sms:' . $phone, 300, $smsCode);
        
        // 记录发送时间
        Redis::setEx('register_sms_last_send:' . $phone, 60, time());
        
        // TODO: 这里应该调用短信服务发送验证码
        // SmsService::send($phone, $smsCode);
        
        return success([
            'message' => '验证码已发送至您的手机，请注意查收',
            'phone' => $phone,
            // 开发环境下返回验证码，生产环境应删除
            'debug_code' => $smsCode
        ]);
    }

    /**
     * 企业注册
     * @param Request $request
     * @return Response
     */
    public function register(Request $request): Response
    {
        $companyName = $request->post('company_name', '');
        $phone = $request->post('phone', '');
        $smsCode = $request->post('sms_code', '');
        $password = $request->post('password', '');
        $confirmPassword = $request->post('confirm_password', '');
        
        // 参数验证
        if (empty($companyName) || empty($phone) || empty($smsCode) || empty($password) || empty($confirmPassword)) {
            return fail('参数不能为空');
        }
        
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            return fail('请输入正确的手机号码');
        }
        
        if ($password !== $confirmPassword) {
            return fail('两次输入的密码不一致');
        }
        
        if (strlen($password) < 5 || strlen($password) > 20) {
            return fail('密码长度应该介于5-20字符之间');
        }
        
        if (!preg_match('/^[A-Za-z0-9\-_]+$/', $password)) {
            return fail('密码只能包含字母和数字及下划线');
        }
        
        // 验证短信验证码
        $storedCode = Redis::get('register_sms:' . $phone);
        if (empty($storedCode)) {
            return fail('验证码已过期，请重新获取');
        }
        
        if ($storedCode !== $smsCode) {
            return fail('验证码错误');
        }
        
        // 再次验证手机号是否已注册
        $existingCompany = CsCompany::where('mobile', $phone)->first();
        if (!empty($existingCompany)) {
            return fail('该手机号已注册，请直接登录');
        }
        
        try {
            // 开始事务
            \support\Db::beginTransaction();
            
            // 创建企业
            $company = new CsCompany();
            $company->company_name = $companyName;
            $company->nickname = $companyName;
            $company->mobile = $phone;
            $company->password = $password;
            $company->status = 1;
            $company->buy_at = date('Y-m-d');
            $company->expired_at = date('Y-m-d', time() + 10 * 86400); // 10天试用期
            $company->product_version_id = 1;
            $company->shop_nums = 1;
            $company->buy_year = 0;
            $company->save();
            
            // 触发企业创建事件（创建默认管理员等）
            $eventParams = [
                'product_version_id' => 1,
                'company_id' => $company->id,
                'company_name' => $companyName,
                'mobile' => $phone,
                'password' => $password,
                'status' => 1,
                'buy_year' => 0,
            ];
            Event::dispatch('company.add', $eventParams);
            
            // 提交事务
            \support\Db::commit();
            
            // 获取刚创建的企业管理员
            $admin = CsCompanyAdmin::where(['username' => $phone])->first();
            if (empty($admin)) {
                throw new \Exception('创建管理员失败');
            }
            
            // 更新登录信息
            $admin->login_ip = $request->getRealIp();
            $admin->login_time = date('Y-m-d H:i:s');
            $admin->save();
            
            // 处理管理员信息
            $admin->avatar = get_image_url($admin->avatar);
            $admin->role_text = $admin?->csCompanyAdminRole?->role_name;
            $rolePermission = $admin?->csCompanyAdminRole?->role_permission;
            unset($admin->csCompanyAdminRole);
            
            // 生成token并存入Redis
            $token = md5('company' . $admin->id . $admin->username . $admin->login_time);
            Redis::setEx('company:' . $token, config('app.token_expire_time'), $admin->id . '-' . $admin->role_id . '-' . $admin->company_id);
            
            // 设置token和菜单信息
            $admin->token = $token;
            $admin->menu = $this->getMenuList(1, $rolePermission);
            
            // 清除验证码
            Redis::del('register_sms:' . $phone);
            Redis::del('register_sms_last_send:' . $phone);
            
            return success($admin);
            
        } catch (\Exception $e) {
            // 回滚事务
            \support\Db::rollBack();
            return fail('注册失败，请重试');
        }
    }

    public function login(Request $request): Response
    {
        $username = $request->post('username','');
        $password = $request->post('password','');
        if (empty($username) || empty($password)) {
            return fail('参数错误');
        }
        $admin = CsCompanyAdmin::where(['username'=>$username])->first();
        if (empty($admin)) {
            return fail('用户不存在');
        }
        if (md5($admin->password) != $password) {
            return fail('账号与密码不匹配');
        }
        if ($admin->status == 0) {
            return fail('该账号已经被冻结，请联系管理员');
        }
        // 判断公司是否过期
        if ($admin->csCompany->expired_at < date('Y-m-d')) {
            return fail('公司授权期限已过，请联系管理员');
        }
        $admin->login_ip = $request->getRealIp();
        $admin->login_time = date('Y-m-d H:i:s');
        $admin->save();
        $admin->avatar = get_image_url($admin->avatar);
        $admin->role_text = $admin?->csCompanyAdminRole?->role_name;
        $rolePermission = $admin?->csCompanyAdminRole?->role_permission;
        unset($admin->csCompanyAdminRole);
        // 用户身份直接存入redis
        $token = md5('company' . $admin->id . $admin->username . $admin->login_time);
        Redis::setEx('company:' . $token, config('app.token_expire_time'), $admin->id . '-' . $admin->role_id . '-' . $admin->company_id);
        $admin->token = $token;
        $admin->menu = $this->getMenuList(1, $rolePermission);
        return success($admin);
    }

    /**
     * 获取客服信息
     * @param Request $request
     * @return Response
     */
    public function getCustomerServiceInfo(Request $request): Response
    {
        $customerServiceInfo = [
            'hotline' => '************8',
            'wechat' => 'CompanyService',
            'email' => '<EMAIL>'
        ];
        
        return success($customerServiceInfo);
    }

    /**
     * 获取用户协议
     * @param Request $request
     * @return Response
     */
    public function getUserAgreement(Request $request): Response
    {
        $userAgreementContent = '
<h1>用户协议</h1>

<p><strong>生效日期：</strong>2024年1月1日</p>

<h2>1. 协议的接受</h2>
<p>欢迎使用我们的企业管理平台服务。通过注册、访问或使用本服务，您表示同意受本用户协议（"协议"）的约束。如果您不同意本协议的条款，请不要使用本服务。</p>

<h2>2. 服务描述</h2>
<p>本平台为企业用户提供以下服务：</p>
<ul>
  <li><strong>企业管理工具：</strong>包括但不限于员工管理、项目管理、财务管理等功能</li>
  <li><strong>数据分析服务：</strong>为企业提供业务数据分析和报告</li>
  <li><strong>云存储服务：</strong>安全可靠的企业数据存储解决方案</li>
  <li><strong>协作工具：</strong>团队协作和沟通工具</li>
</ul>

<h2>3. 用户责任</h2>
<p>作为本服务的用户，您同意：</p>
<ol>
  <li>提供真实、准确、完整的注册信息</li>
  <li>维护账户信息的安全性和保密性</li>
  <li>不得将账户转让给第三方</li>
  <li>遵守所有适用的法律法规</li>
  <li>不得从事任何可能损害平台或其他用户利益的行为</li>
</ol>

<h2>4. 知识产权</h2>
<p>本平台及其所有内容、功能和服务均受版权、商标和其他知识产权法律保护。未经明确书面许可，您不得复制、修改、分发或以其他方式使用本平台的任何部分。</p>

<h2>5. 隐私保护</h2>
<p>我们重视您的隐私权。有关我们如何收集、使用和保护您的个人信息的详细信息，请参阅我们的<a href="#" onclick="return false;">隐私政策</a>。</p>

<h2>6. 服务变更</h2>
<p>我们保留随时修改、暂停或终止服务的权利，恕不另行通知。我们将尽力提前通知重大变更。</p>

<h2>7. 免责声明</h2>
<p>本服务按"现状"提供，我们不对服务的可用性、准确性或完整性做出任何明示或暗示的保证。</p>

<h2>8. 联系我们</h2>
<p>如果您对本协议有任何疑问，请通过以下方式联系我们：</p>
<ul>
  <li>邮箱：<EMAIL></li>
  <li>电话：************</li>
  <li>地址：北京市朝阳区xxx路xxx号</li>
</ul>
';
        
        return success([
            'title' => '用户协议',
            'content' => $userAgreementContent,
            'updated_at' => '2024-01-01'
        ]);
    }

    /**
     * 获取隐私政策
     * @param Request $request
     * @return Response
     */
    public function getPrivacyPolicy(Request $request): Response
    {
        $privacyPolicyContent = '
<h1>隐私政策</h1>

<p><strong>最后更新日期：</strong>2024年1月1日</p>

<h2>1. 信息收集</h2>
<p>我们可能收集以下类型的信息：</p>

<h3>1.1 个人信息</h3>
<ul>
  <li><strong>注册信息：</strong>企业名称、联系人姓名、电话号码、邮箱地址</li>
  <li><strong>身份验证信息：</strong>用户名、密码（加密存储）</li>
  <li><strong>企业信息：</strong>营业执照、组织机构代码等企业资质信息</li>
</ul>

<h3>1.2 使用信息</h3>
<ul>
  <li><strong>日志信息：</strong>IP地址、浏览器类型、访问时间、页面访问记录</li>
  <li><strong>设备信息：</strong>设备类型、操作系统、设备标识符</li>
  <li><strong>使用数据：</strong>功能使用情况、操作记录</li>
</ul>

<h2>2. 信息使用</h2>
<p>我们使用收集的信息用于：</p>
<ol>
  <li><strong>提供服务：</strong>为您提供平台功能和技术支持</li>
  <li><strong>账户管理：</strong>创建和维护您的账户</li>
  <li><strong>安全保护：</strong>检测和防范安全威胁</li>
  <li><strong>服务改进：</strong>分析使用情况以改进我们的服务</li>
  <li><strong>法律合规：</strong>遵守适用的法律法规要求</li>
</ol>

<h2>3. 信息共享</h2>
<p>我们不会出售、租赁或以其他方式向第三方披露您的个人信息，除非：</p>
<ul>
  <li>获得您的明确同意</li>
  <li>法律法规要求</li>
  <li>保护我们的合法权益</li>
  <li>与可信的服务提供商合作（在严格的保密协议下）</li>
</ul>

<h2>4. 数据安全</h2>
<p>我们采取以下措施保护您的信息安全：</p>
<ul>
  <li><strong>加密传输：</strong>使用SSL/TLS加密技术保护数据传输</li>
  <li><strong>访问控制：</strong>严格限制对个人信息的访问权限</li>
  <li><strong>安全审计：</strong>定期进行安全评估和漏洞扫描</li>
  <li><strong>数据备份：</strong>建立完善的数据备份和恢复机制</li>
</ul>

<h2>5. Cookie使用</h2>
<p>我们使用Cookie和类似技术来：</p>
<ul>
  <li>记住您的登录状态</li>
  <li>分析网站使用情况</li>
  <li>提供个性化体验</li>
  <li>改进服务质量</li>
</ul>
<p>您可以通过浏览器设置管理Cookie偏好。</p>

<h2>6. 数据保留</h2>
<p>我们将在以下期限内保留您的个人信息：</p>
<ul>
  <li><strong>账户信息：</strong>账户存续期间及注销后3年</li>
  <li><strong>交易记录：</strong>根据法律要求保留相应期限</li>
  <li><strong>日志信息：</strong>通常保留6个月</li>
</ul>

<h2>7. 您的权利</h2>
<p>根据适用的法律法规，您享有以下权利：</p>
<ul>
  <li><strong>访问权：</strong>查看我们持有的您的个人信息</li>
  <li><strong>更正权：</strong>要求更正不准确的个人信息</li>
  <li><strong>删除权：</strong>在特定情况下要求删除个人信息</li>
  <li><strong>限制处理权：</strong>限制我们处理您的个人信息</li>
  <li><strong>数据可携权：</strong>以结构化格式获取您的个人信息</li>
</ul>

<h2>8. 未成年人保护</h2>
<p>我们的服务面向企业用户，不会故意收集未满18周岁未成年人的个人信息。如果我们发现收集了未成年人的信息，将立即删除。</p>

<h2>9. 政策更新</h2>
<p>我们可能会不时更新本隐私政策。重大变更将通过网站公告或邮件通知您。继续使用服务即表示您接受更新后的政策。</p>

<h2>10. 联系我们</h2>
<p>如果您对本隐私政策有任何疑问或需要行使您的权利，请联系我们：</p>
<ul>
  <li><strong>数据保护官邮箱：</strong><EMAIL></li>
  <li><strong>客服电话：</strong>************</li>
  <li><strong>通信地址：</strong>北京市朝阳区xxx路xxx号</li>
</ul>
';
        
        return success([
            'title' => '隐私政策',
            'content' => $privacyPolicyContent,
            'updated_at' => '2024-01-01'
        ]);
    }

    /**
     * 忘记密码 - 验证手机号并发送验证码
     * @param Request $request
     * @return Response
     */
    public function sendForgotPasswordSms(Request $request): Response
    {
        $phone = $request->post('phone', '');
        if (empty($phone)) {
            return fail('手机号不能为空');
        }
        
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            return fail('请输入正确的手机号码');
        }
        
        // 验证手机号是否是合法的授权企业手机号
        $company = CsCompany::where('mobile', $phone)->where('status', 1)->first();
        if (empty($company)) {
            return fail('该手机号未注册或账户已被冻结');
        }
        
        // 检查企业是否过期
        if ($company->expired_at < date('Y-m-d')) {
            return fail('企业授权已过期，请联系管理员');
        }
        
        // 生成6位随机验证码
        $smsCode = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        // 将验证码存储到Redis，设置5分钟过期
        Redis::setEx('forgot_password_sms:' . $phone, 300, $smsCode);
        
        // TODO: 这里应该调用短信服务发送验证码
        // SmsService::send($phone, $smsCode);
        
        return success([
            'message' => '验证码已发送至您的手机，请注意查收',
            'phone' => $phone,
            // 开发环境下返回验证码，生产环境应删除
            'debug_code' => $smsCode
        ]);
    }

    /**
     * 忘记密码 - 验证短信验证码
     * @param Request $request
     * @return Response
     */
    public function verifyForgotPasswordSms(Request $request): Response
    {
        $phone = $request->post('phone', '');
        $smsCode = $request->post('sms_code', '');
        
        if (empty($phone) || empty($smsCode)) {
            return fail('手机号和验证码不能为空');
        }
        
        // 从Redis获取验证码
        $storedCode = Redis::get('forgot_password_sms:' . $phone);
        if (empty($storedCode)) {
            return fail('验证码已过期，请重新获取');
        }
        
        if ($storedCode !== $smsCode) {
            return fail('验证码错误');
        }
        
        // 验证成功，生成重置token
        $resetToken = md5('reset_password_' . $phone . '_' . time() . '_' . rand(1000, 9999));
        
        // 将重置token存储到Redis，设置10分钟过期
        Redis::setEx('reset_password_token:' . $phone, 600, $resetToken);
        
        // 清除验证码
        Redis::del('forgot_password_sms:' . $phone);
        
        return success([
            'message' => '验证码验证成功',
            'token' => $resetToken
        ]);
    }

    /**
     * 忘记密码 - 重置密码
     * @param Request $request
     * @return Response
     */
    public function resetPassword(Request $request): Response
    {
        $phone = $request->post('phone', '');
        $token = $request->post('token', '');
        $newPassword = $request->post('new_password', '');
        $confirmPassword = $request->post('confirm_password', '');
        
        if (empty($phone) || empty($token) || empty($newPassword) || empty($confirmPassword)) {
            return fail('参数不能为空');
        }
        
        if ($newPassword !== $confirmPassword) {
            return fail('两次输入的密码不一致');
        }
        
        if (strlen($newPassword) < 5 || strlen($newPassword) > 20) {
            return fail('密码长度应该介于5-20字符之间');
        }
        
        if (!preg_match('/^[A-Za-z0-9\-_]+$/', $newPassword)) {
            return fail('密码只能包含字母和数字及下划线');
        }
        
        // 验证重置token
        $storedToken = Redis::get('reset_password_token:' . $phone);
        if (empty($storedToken) || $storedToken !== $token) {
            return fail('重置链接已过期，请重新操作');
        }
        
        // 查找企业管理员
        $admin = CsCompanyAdmin::where('username', $phone)->first();
        if (empty($admin)) {
            return fail('用户不存在');
        }
        
        // 更新密码
        $admin->password = $newPassword;
        $admin->save();
        
        // 清除重置token
        Redis::del('reset_password_token:' . $phone);
        
        return success([
            'message' => '密码重置成功，请使用新密码登录'
        ]);
    }

    /**
     * 忘记密码 - 重新发送验证码
     * @param Request $request
     * @return Response
     */
    public function resendForgotPasswordSms(Request $request): Response
    {
        $phone = $request->post('phone', '');
        if (empty($phone)) {
            return fail('手机号不能为空');
        }
        
        // 检查是否频繁发送（60秒内只能发送一次）
        $lastSendTime = Redis::get('forgot_password_sms_last_send:' . $phone);
        if (!empty($lastSendTime) && (time() - $lastSendTime) < 60) {
            return fail('请稍后再试，验证码发送过于频繁');
        }
        
        // 验证手机号是否是合法的授权企业手机号
        $company = CsCompany::where('mobile', $phone)->where('status', 1)->first();
        if (empty($company)) {
            return fail('该手机号未注册或账户已被冻结');
        }
        
        // 生成6位随机验证码
        $smsCode = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        // 将验证码存储到Redis，设置5分钟过期
        Redis::setEx('forgot_password_sms:' . $phone, 300, $smsCode);
        
        // 记录发送时间
        Redis::setEx('forgot_password_sms_last_send:' . $phone, 60, time());
        
        // TODO: 这里应该调用短信服务发送验证码
        // SmsService::send($phone, $smsCode);
        
        return success([
            'message' => '验证码已重新发送',
            // 开发环境下返回验证码，生产环境应删除
            'debug_code' => $smsCode
        ]);
    }

    /**
     * 获取用户信息（包含企业版本信息）
     * @param Request $request
     * @return Response
     */
    public function getUserInfo(Request $request): Response
    {
        $adminId = $request->admin_id;
        
        // 获取管理员信息
        $admin = CsCompanyAdmin::with(['csCompany.csProductVersion', 'csCompanyAdminRole'])
                    ->find($adminId);
        
        if (empty($admin)) {
            return fail('用户不存在');
        }
        
        // 获取企业信息
        $company = $admin->csCompany;
        if (empty($company)) {
            return fail('企业信息不存在');
        }
        
        // 获取版本信息
        $productVersion = $company->csProductVersion;
        
        // 计算剩余天数
        $expiredAt = $company->expired_at;
        $remainingDays = max(0, ceil((strtotime($expiredAt) - time()) / 86400));
        $isExpired = $expiredAt < date('Y-m-d');
        
        // 组装返回数据
        $userData = [
            'id' => $admin->id,
            'username' => $admin->username,
            'nickname' => $admin->nickname,
            'avatar' => get_image_url($admin->avatar),
            'role_text' => $admin->csCompanyAdminRole?->role_name ?? '',
            'login_time' => $admin->login_time,
            'login_ip' => $admin->login_ip,
            
            // 企业信息
            'company_info' => [
                'id' => $company->id,
                'company_name' => $company->company_name,
                'mobile' => $company->mobile,
                'status' => $company->status,
                'buy_at' => $company->buy_at,
                'expired_at' => $company->expired_at,
                'remaining_days' => $remainingDays,
                'is_expired' => $isExpired,
                'shop_nums' => $company->shop_nums,
                'buy_year' => $company->buy_year,
            ],
            
            // 版本信息
            'version_info' => [
                'id' => $productVersion?->id ?? 0,
                'version_name' => $productVersion?->version_name ?? '未知版本',
                'product_price' => $productVersion?->product_price ?? 0,
                'continue_price' => $productVersion?->continue_price ?? 0,
                'limit_nums' => $productVersion?->limit_nums ?? 0,
                'remark' => $productVersion?->remark ?? '',
            ]
        ];
        
        return success($userData);
    }

    /**
     * 退出登录
     * @param Request $request
     * @return Response
     */
    public function logout(Request $request): Response
    {
        $token = $request->header('token', '');
        if (!empty($token)) {
            Redis::del('company:' . $token);
        }
        return success();
    }

    /**
     * 修改密码
     * @param Request $request
     * @return Response
     */
    public function savePwd(Request $request): Response
    {
        $mobile = $request->post('mobile','');
        $vsCode = $request->post('vs_code','');
        $password = $request->post('password','');
        $conPassword = $request->post('con_password','');
        if (empty($mobile) || empty($vsCode) || empty($password) || empty($conPassword)) {
            return fail('参数为空');
        }
        if ($password != $conPassword) {
            return  fail('两次输入的密码不一致');
        }
        if (strlen($password) < 5 || strlen($password) > 20) {
            return fail('密码长度应该介于5-20字符之间');
        }
        if (!preg_match('/^[A-Za-z0-9\-\_]+$/',$password)) {
            return fail('密码只能包含字母和数字及下划线');
        }
        $admin = CsCompanyAdmin::where(['username' => $mobile])->first();
        if (empty($admin)) {
            return fail('用户不存在');
        }
        if ($admin->status == 0) {
            return fail('该账号已经被冻结，请联系管理员');
        }
        $admin->password = $password;
        $admin->save();
        return success();
    }

}
