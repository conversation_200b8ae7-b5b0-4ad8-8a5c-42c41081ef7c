<?php

namespace app\company\controller;

use app\controller\CompanyBaseController;
use app\service\RoleMenu;
use support\Request;
use support\Response;
use think\exception\ValidateException;

class CsCompanyAdminRoleController extends CompanyBaseController
{
    use RoleMenu;
    // 验证器
    protected $validateName = 'CsCompanyAdminRoleValidate';

    // 当前主模型
    protected $modelName = 'CsCompanyAdminRole';

    /**
     * 角色权限菜单
     * @param Request $request
     * @return Response
     */
    public function add(Request $request): Response
    {
        $menuList = $this->getMenuList(1, '');
        return success(['menu' => $menuList]);
    }

    /**
     * 修改查看
     * @param Request $request
     * @return Response
     */
    public function edit(Request $request): Response
    {
        $id = $request->get('id',0);
        if (empty($id)) {
            return fail('参数错误');
        }
        $model = 'app\model\\' . $this->modelName;
        $info = $model::edit($id);
        $info['menu'] = $this->getMenuList(1,$info['role_permission']);
        return success($info);
    }

    /**
     * 修改保存
     * @param Request $request
     * @return Response
     */
    public function editPost(Request $request): Response
    {
        try {
            $model = 'app\model\\' . $this->modelName;
            $data = $request->onlyPost($model::$validateFields);
            if (empty($data['id'])) {
                throw new \Exception('参数错误');
            }
            if ($data['id'] == 1) {
                throw new \Exception('超级管理员不允许修改');
            }
            $data['company_id'] = $request->company_id;
            $validate = new ('app\validate\\' . $this->validateName);
            if (!$validate->check($data)) {
                throw new ValidateException($validate->getError());
            }
            $result = $model::editPost($data);
            if (!empty($result['error'])) {
                throw new \Exception($result['msg']);
            }
            return success('修改成功');
        } catch (\Exception $e) {
            $errMsg = $e->getMessage();
        } catch (ValidateException $e) {
            $errMsg = $e->getError();
        }
        return fail($errMsg);
    }

    /**
     * 单个删除
     * @param Request $request
     * @return Response
     */
    public function del(Request $request): Response
    {
        $id = $request->post('id', 0);
        if (empty($id)) {
            return fail('参数错误');
        }
        if ($id == 1) {
            return fail('超级管理员不允许删除');
        }
        $model = 'app\model\\' . $this->modelName;
        $result = $model::del($id);
        if (!empty($result['error'])) {
            return fail($result['msg']);
        }
        return success('删除成功');
    }

    /**
     * 批量删除
     * @param Request $request
     * @return Response
     */
    public function selectDel(Request $request): Response
    {
        $ids = $request->post('ids', '');
        if (empty($ids)) {
            return fail('参数错误');
        }
        $ids = explode(',', $ids);
        if (in_array(1, $ids)) {
            return fail('超级管理员不允许删除');
        }
        $model = 'app\model\\' . $this->modelName;
        $result = $model::selectDel($ids);
        if (!empty($result['error'])) {
            return fail($result['msg']);
        }
        return success('删除成功');
    }

    protected function getListWhere(): array
    {
        $where = [];
        $where[] = ['id', '>', 1];
        return $where;
    }

    /**
     * 获取角色列表（主要用于添加和修改）
     * @param Request $request
     * @return Response
     */
    public function getRoleList(Request $request): Response
    {
        $model = 'app\model\\' . $this->modelName;
        $itemList = $model::where('id', '=', 1)->orWhere('company_id', '=', $request->company_id)
            ->select(['id', 'role_name'])->get();
        return success($itemList);
    }

}
