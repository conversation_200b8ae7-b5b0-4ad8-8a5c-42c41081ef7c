<?php

namespace app\company\service;

use app\model\CsShop;
use support\Log;
use support\Redis;

/**
 * Company端数据安全服务
 * 确保严格的多租户数据隔离
 */
class CompanyDataSecurityService
{
    /**
     * 验证门店是否属于当前公司
     * @param int $companyId
     * @param int $shopId
     * @return bool
     */
    public static function validateShopOwnership(int $companyId, int $shopId): bool
    {
        try {
            $cacheKey = "company_shop_ownership:{$companyId}:{$shopId}";
            
            // 先从Redis缓存获取
            $cached = Redis::get($cacheKey);
            if ($cached !== null) {
                return (bool)$cached;
            }
            
            // 数据库验证
            $exists = CsShop::where('company_id', $companyId)
                ->where('id', $shopId)
                ->exists();
            
            // 缓存结果（5分钟）
            Redis::setex($cacheKey, 300, $exists ? 1 : 0);
            
            return $exists;
        } catch (\Exception $e) {
            Log::error('验证门店归属失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取公司的所有门店ID
     * @param int $companyId
     * @return array
     */
    public static function getCompanyShopIds(int $companyId): array
    {
        try {
            $cacheKey = "company_shop_ids:{$companyId}";
            
            // 先从Redis缓存获取
            $cached = Redis::get($cacheKey);
            if ($cached !== null) {
                return json_decode($cached, true) ?: [];
            }
            
            // 数据库查询
            $shopIds = CsShop::where('company_id', $companyId)
                ->pluck('id')
                ->toArray();
            
            // 缓存结果（10分钟）
            Redis::setex($cacheKey, 600, json_encode($shopIds));
            
            return $shopIds;
        } catch (\Exception $e) {
            Log::error('获取公司门店ID失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 清除公司相关缓存
     * @param int $companyId
     */
    public static function clearCompanyCache(int $companyId): void
    {
        try {
            $keys = [
                "company_shop_ids:{$companyId}",
                "company_config:{$companyId}",
                "company_stats:{$companyId}"
            ];
            
            foreach ($keys as $key) {
                Redis::del($key);
            }
            
            // 清除门店相关缓存
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id');
            foreach ($shopIds as $shopId) {
                Redis::del("company_shop_ownership:{$companyId}:{$shopId}");
            }
        } catch (\Exception $e) {
            Log::error('清除公司缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 数据查询安全检查
     * @param int $companyId
     * @param array $shopIds 要查询的门店ID
     * @return array 安全的门店ID数组
     */
    public static function secureShopIds(int $companyId, array $shopIds): array
    {
        $companyShopIds = self::getCompanyShopIds($companyId);
        return array_intersect($shopIds, $companyShopIds);
    }
}