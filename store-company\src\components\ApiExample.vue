<template>
  <div class="api-example">
    <el-card header="API 请求工具示例">
      <div class="example-section">
        <h3>1. 管理员列表示例</h3>
        <el-button @click="fetchAdminList" :loading="adminLoading">
          获取管理员列表
        </el-button>
        <div v-if="adminList.length > 0" class="result-display">
          <p>共 {{ adminTotal }} 条记录</p>
          <ul>
            <li v-for="admin in adminList" :key="admin.id">
              {{ admin.username }} - {{ admin.nickname }}
            </li>
          </ul>
        </div>
      </div>

      <div class="example-section">
        <h3>2. 角色列表示例</h3>
        <el-button @click="fetchRoleList" :loading="roleLoading">
          获取角色列表
        </el-button>
        <div v-if="roleList.length > 0" class="result-display">
          <p>共 {{ roleTotal }} 条记录</p>
          <ul>
            <li v-for="role in roleList" :key="role.id">
              {{ role.role_name }} - {{ role.description }}
            </li>
          </ul>
        </div>
      </div>

      <div class="example-section">
        <h3>3. 门店列表示例</h3>
        <el-button @click="fetchShopList" :loading="shopLoading">
          获取门店列表
        </el-button>
        <div v-if="shopList.length > 0" class="result-display">
          <p>共 {{ shopTotal }} 条记录</p>
          <ul>
            <li v-for="shop in shopList" :key="shop.id">
              {{ shop.shop_name }} - {{ shop.contact_person }}
            </li>
          </ul>
        </div>
      </div>

      <div class="example-section">
        <h3>4. 错误处理示例</h3>
        <el-button @click="testErrorHandling" :loading="errorLoading">
          测试错误处理
        </el-button>
        <p class="note">
          这将发送一个错误的请求来测试错误处理机制
        </p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  getAdminList, 
  getRoleList, 
  getShopList,
  service,
  type Admin,
  type Role,
  type Shop
} from '@/services'
import { ElMessage } from 'element-plus'

// 管理员相关状态
const adminList = ref<Admin[]>([])
const adminTotal = ref(0)
const adminLoading = ref(false)

// 角色相关状态
const roleList = ref<Role[]>([])
const roleTotal = ref(0)
const roleLoading = ref(false)

// 门店相关状态
const shopList = ref<Shop[]>([])
const shopTotal = ref(0)
const shopLoading = ref(false)

// 错误处理测试状态
const errorLoading = ref(false)

// 获取管理员列表
const fetchAdminList = async () => {
  try {
    adminLoading.value = true
    const result = await getAdminList({
      page: 1,
      page_size: 5
    })
    
    adminList.value = result.itemList
    adminTotal.value = result.total
    ElMessage.success('获取管理员列表成功')
  } catch (error) {
    // 错误已在响应拦截器中处理
    console.error('获取管理员列表失败:', error)
  } finally {
    adminLoading.value = false
  }
}

// 获取角色列表
const fetchRoleList = async () => {
  try {
    roleLoading.value = true
    const result = await getRoleList({
      page: 1,
      page_size: 5
    })
    
    roleList.value = result.itemList
    roleTotal.value = result.total
    ElMessage.success('获取角色列表成功')
  } catch (error) {
    console.error('获取角色列表失败:', error)
  } finally {
    roleLoading.value = false
  }
}

// 获取门店列表
const fetchShopList = async () => {
  try {
    shopLoading.value = true
    const result = await getShopList({
      page: 1,
      page_size: 5
    })
    
    shopList.value = result.itemList
    shopTotal.value = result.total
    ElMessage.success('获取门店列表成功')
  } catch (error) {
    console.error('获取门店列表失败:', error)
  } finally {
    shopLoading.value = false
  }
}

// 测试错误处理
const testErrorHandling = async () => {
  try {
    errorLoading.value = true
    // 发送一个不存在的接口请求来测试错误处理
    await service.get('/company/NonExistentModule/index')
  } catch (error) {
    console.log('错误处理测试完成，错误信息已在拦截器中处理')
  } finally {
    errorLoading.value = false
  }
}
</script>

<style scoped>
.api-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.example-section:last-child {
  border-bottom: none;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.result-display {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.result-display ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.result-display li {
  margin-bottom: 5px;
}

.note {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
}
</style>
