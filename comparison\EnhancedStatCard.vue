<template>
  <div 
    class="enhanced-stat-card"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    :class="{ 'loading': loading }"
  >
    <!-- 卡片头部 -->
    <div class="stat-header">
      <div class="stat-icon" :class="data.iconColor">
        {{ data.icon }}
      </div>
      <div class="comparison-mode-badge">
        {{ comparisonModeText }}
      </div>
    </div>

    <!-- 主要数值 -->
    <div class="stat-value">
      <CountUp 
        :end-val="typeof data.value === 'number' ? data.value : 0"
        :options="countUpOptions"
        v-if="typeof data.value === 'number'"
      />
      <span v-else>{{ data.value }}</span>
    </div>
    
    <!-- 标签 -->
    <div class="stat-label">{{ data.title }}</div>

    <!-- 增强的对比信息区域 -->
    <div 
      class="enhanced-comparison-info comparison-appear" 
      v-if="!loading && data.comparison"
      :class="{ 'expanded': isExpanded }"
    >
      <div class="comparison-header">
        <span class="comparison-mode">{{ comparisonModeText }}对比</span>
        <span class="comparison-period">{{ basePeriod }}</span>
      </div>
      
      <div class="comparison-details">
        <!-- 主要对比指标 -->
        <div class="comparison-item">
          <div 
            class="comparison-value"
            :class="getComparisonClass(data.comparison.trend)"
          >
            <span class="comparison-icon" :class="data.comparison.trend"></span>
            <span class="percentage">
              {{ formatPercentage(data.comparison.percent) }}%
            </span>
          </div>
          <div class="comparison-label">增长率</div>
          <div class="absolute-change">
            较{{ basePeriod }}{{ data.comparison.trend === 'up' ? '增加' : data.comparison.trend === 'down' ? '减少' : '持平' }}
            {{ formatAbsoluteChange(data.comparison.absolute) }}
          </div>
        </div>

        <!-- 次要指标（如果有） -->
        <div class="comparison-item" v-if="data.secondaryMetric">
          <div 
            class="comparison-value"
            :class="getComparisonClass(data.secondaryMetric.comparison?.trend || 'equal')"
          >
            <span class="comparison-icon" :class="data.secondaryMetric.comparison?.trend || 'equal'"></span>
            <span v-if="data.secondaryMetric.comparison">
              {{ formatPercentage(data.secondaryMetric.comparison.percent) }}%
            </span>
            <span v-else>{{ data.secondaryMetric.value }}</span>
          </div>
          <div class="comparison-label">{{ data.secondaryMetric.label }}</div>
          <div class="absolute-change" v-if="data.secondaryMetric.comparison">
            {{ data.secondaryMetric.comparison.additionalInfo || '与上期对比' }}
          </div>
        </div>
      </div>

      <!-- 额外信息 -->
      <div class="additional-info" v-if="data.comparison.consecutivePeriods || data.comparison.additionalInfo">
        <div class="info-item" v-if="data.comparison.consecutivePeriods">
          <span class="info-icon">🔥</span>
          <span>连续{{ data.comparison.consecutivePeriods }}{{ getPeriodUnit() }}{{ data.comparison.trend === 'up' ? '上升' : '下降' }}</span>
        </div>
        <div class="info-item" v-if="data.comparison.additionalInfo">
          <span class="info-icon">💡</span>
          <span>{{ data.comparison.additionalInfo }}</span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="comparison-loading" v-if="loading">
      <div class="loading-spinner"></div>
      正在加载对比数据...
    </div>

    <!-- 悬停提示 -->
    <Teleport to="body">
      <div 
        class="comparison-tooltip"
        v-if="showTooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-title">{{ data.title }} - 详细分析</div>
        <div class="tooltip-content">
          <div class="tooltip-row">
            <span>当前值:</span>
            <span>{{ formatValue(data.value) }}</span>
          </div>
          <div class="tooltip-row">
            <span>{{ basePeriod }}:</span>
            <span>{{ formatValue(calculateBasePeriodValue()) }}</span>
          </div>
          <div class="tooltip-row">
            <span>变化幅度:</span>
            <span :class="getComparisonClass(data.comparison?.trend || 'equal')">
              {{ formatPercentage(data.comparison?.percent || 0) }}%
            </span>
          </div>
          <div class="tooltip-row" v-if="data.comparison?.trend === 'up'">
            <span>排名:</span>
            <span>📈 表现优秀</span>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';

interface ComparisonData {
  percent: number;
  absolute: number;
  trend: 'up' | 'down' | 'equal';
  consecutivePeriods?: number;
  additionalInfo?: string;
}

interface StatData {
  id: string;
  title: string;
  value: number | string;
  icon: string;
  iconColor: 'blue' | 'green' | 'orange' | 'purple';
  comparison?: ComparisonData;
  secondaryMetric?: {
    label: string;
    value: string;
    comparison?: ComparisonData;
  };
}

interface Props {
  data: StatData;
  comparisonMode: 'yoy' | 'mom';
  basePeriod: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<{
  hover: [statId: string, event: MouseEvent];
  click: [statId: string];
}>();

// 响应式状态
const isExpanded = ref(false);
const showTooltip = ref(false);
const tooltipStyle = ref({});

// 计算属性
const comparisonModeText = computed(() => {
  return props.comparisonMode === 'yoy' ? '同比' : '环比';
});

const countUpOptions = computed(() => ({
  duration: 2,
  useEasing: true,
  useGrouping: true,
  separator: ',',
  decimal: '.',
  prefix: props.data.id === 'revenue' ? '¥' : '',
}));

// 方法
const getComparisonClass = (trend: string) => {
  switch (trend) {
    case 'up':
      return 'positive';
    case 'down':
      return 'negative';
    default:
      return 'neutral';
  }
};

const formatPercentage = (value: number) => {
  return value > 0 ? `+${value.toFixed(1)}` : value.toFixed(1);
};

const formatAbsoluteChange = (value: number) => {
  if (props.data.id === 'revenue') {
    return `¥${Math.abs(value).toLocaleString()}`;
  } else if (props.data.id === 'members') {
    return `${Math.abs(value)}位`;
  } else {
    return `${Math.abs(value)}单`;
  }
};

const formatValue = (value: number | string) => {
  if (typeof value === 'number') {
    if (props.data.id === 'revenue') {
      return `¥${value.toLocaleString()}`;
    }
    return value.toLocaleString();
  }
  return value;
};

const calculateBasePeriodValue = () => {
  if (typeof props.data.value === 'number' && props.data.comparison) {
    return props.data.value - props.data.comparison.absolute;
  }
  return 0;
};

const getPeriodUnit = () => {
  if (props.comparisonMode === 'yoy') {
    return '年';
  }
  return props.basePeriod.includes('日') ? '日' : 
         props.basePeriod.includes('周') ? '周' : 
         props.basePeriod.includes('月') ? '月' : '期';
};

const handleMouseEnter = (event: MouseEvent) => {
  isExpanded.value = true;
  showTooltip.value = true;
  
  // 计算提示框位置
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  tooltipStyle.value = {
    position: 'fixed',
    top: `${rect.top - 10}px`,
    left: `${rect.right + 10}px`,
    zIndex: 9999
  };
  
  emit('hover', props.data.id, event);
};

const handleMouseLeave = () => {
  isExpanded.value = false;
  showTooltip.value = false;
};

const handleClick = () => {
  emit('click', props.data.id);
};
</script>

<style scoped>
.enhanced-stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px var(--shadow-light);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.enhanced-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.enhanced-stat-card.loading {
  pointer-events: none;
  opacity: 0.7;
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.blue { background: var(--gradient-primary); }
.stat-icon.green { background: linear-gradient(135deg, #67C23A, #5cb85c); }
.stat-icon.orange { background: linear-gradient(135deg, #E6A23C, #f39c12); }
.stat-icon.purple { background: linear-gradient(135deg, #9C27B0, #8e24aa); }

.comparison-mode-badge {
  font-size: 11px;
  color: var(--text-muted);
  background: rgba(27, 54, 93, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 16px;
}

.enhanced-comparison-info {
  background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(240,242,245,0.5));
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
  border: 1px solid rgba(27, 54, 93, 0.08);
  transition: all 0.3s ease;
}

.enhanced-comparison-info.expanded {
  padding: 16px;
  background: linear-gradient(135deg, rgba(240,248,255,0.9), rgba(230,240,250,0.7));
}

.comparison-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comparison-mode {
  font-size: 11px;
  color: var(--text-muted);
  background: rgba(27, 54, 93, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.comparison-period {
  font-size: 11px;
  color: var(--text-muted);
}

.comparison-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.comparison-value {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  font-size: 14px;
}

.comparison-value.positive {
  color: var(--success-color);
}

.comparison-value.negative {
  color: var(--danger-color);
}

.comparison-value.neutral {
  color: var(--info-color);
}

.comparison-icon {
  font-size: 16px;
  animation: pulse 2s infinite;
}

.comparison-icon.up::before {
  content: '📈';
}

.comparison-icon.down::before {
  content: '📉';
}

.comparison-icon.equal::before {
  content: '➖';
}

.comparison-label {
  font-size: 11px;
  color: var(--text-muted);
  line-height: 1.2;
}

.absolute-change {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 2px;
}

.additional-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(27, 54, 93, 0.06);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.info-icon {
  font-size: 14px;
}

.comparison-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-muted);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--accent-platinum);
  border-top: 2px solid var(--primary-business-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.comparison-tooltip {
  background: rgba(13, 27, 42, 0.95);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  backdrop-filter: blur(10px);
  max-width: 280px;
  pointer-events: none;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 6px;
  color: var(--accent-soft-gold);
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.comparison-appear {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .enhanced-stat-card {
    padding: 16px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .comparison-tooltip {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
}
</style>