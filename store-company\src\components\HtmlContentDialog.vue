<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h2 class="dialog-title">{{ title }}</h2>
        <button class="dialog-close" @click="close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
      
      <div class="dialog-content">
        <div class="html-content" v-html="content"></div>
      </div>
      
      <div class="dialog-footer">
        <button class="dialog-btn dialog-btn-primary" @click="close">
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  visible: boolean
  title: string
  content: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const close = () => {
  emit('update:visible', false)
  emit('close')
}

const handleOverlayClick = () => {
  close()
}

// Prevent body scroll when dialog is open
watch(() => props.visible, (newVal) => {
  if (newVal) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 0;
  padding-bottom: 16px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #0D1B2A;
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
}

.html-content {
  line-height: 1.6;
  color: #374151;
}

/* HTML content styling */
.html-content h1,
.html-content h2,
.html-content h3,
.html-content h4,
.html-content h5,
.html-content h6 {
  color: #0D1B2A;
  margin-top: 24px;
  margin-bottom: 12px;
  font-weight: 600;
}

.html-content h1 {
  font-size: 24px;
}

.html-content h2 {
  font-size: 20px;
}

.html-content h3 {
  font-size: 18px;
}

.html-content h4 {
  font-size: 16px;
}

.html-content p {
  margin-bottom: 16px;
}

.html-content ul,
.html-content ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.html-content li {
  margin-bottom: 8px;
}

.html-content strong {
  font-weight: 600;
  color: #0D1B2A;
}

.html-content em {
  font-style: italic;
}

.html-content a {
  color: #415A77;
  text-decoration: underline;
}

.html-content a:hover {
  color: #0D1B2A;
}

.html-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: #6b7280;
}

.html-content code {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.html-content pre {
  background: #f3f4f6;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

.html-content pre code {
  background: none;
  padding: 0;
}

.html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.html-content th,
.html-content td {
  border: 1px solid #e5e7eb;
  padding: 12px;
  text-align: left;
}

.html-content th {
  background: #f9fafb;
  font-weight: 600;
}

.dialog-footer {
  padding: 16px 32px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-btn {
  padding: 10px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.dialog-btn-primary {
  background: linear-gradient(135deg, #1B365D 0%, #0D1B2A 100%);
  color: white;
}

.dialog-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 27, 42, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .dialog-container {
    margin: 16px;
    max-height: calc(100vh - 32px);
  }
  
  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .dialog-title {
    font-size: 18px;
  }
}

/* Custom scrollbar for content */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>