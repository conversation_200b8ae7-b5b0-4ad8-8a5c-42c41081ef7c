<template>
  <div class="customer-service-dialog">
    <el-dialog
      v-model="visible"
      
      width="500px"
      center
      @close="handleClose"
    >
      <div class="service-content" v-if="serviceInfo">
        <!-- 客服头像和标题 -->
        <div class="service-header">
          <div class="service-avatar">
            <div class="avatar-icon">💬</div>
          </div>
          <div class="service-title">
            <h3>我们的客服团队</h3>
            <p>随时为您提供专业服务支持</p>
          </div>
        </div>

        <!-- 联系方式列表 -->
        <div class="contact-list">
          <!-- 客服热线 -->
          <div class="contact-item">
            <div class="contact-icon phone">📞</div>
            <div class="contact-info">
              <div class="contact-label">客服热线</div>
              <div class="contact-value">{{ serviceInfo.hotline }}</div>
              <div class="contact-desc">工作时间：周一至周日 9:00-18:00</div>
            </div>
            <el-button 
              type="primary" 
              size="small" 
              @click="copyPhone(serviceInfo.hotline)"
              class="contact-action"
            >
              复制
            </el-button>
          </div>

          <!-- 微信客服 -->
          <div class="contact-item">
            <div class="contact-icon wechat">💬</div>
            <div class="contact-info">
              <div class="contact-label">微信客服</div>
              <div class="contact-value">{{ serviceInfo.wechat }}</div>
              <div class="contact-desc">添加微信好友，获得1对1专属服务</div>
            </div>
            <el-button 
              type="success" 
              size="small" 
              @click="copyWechat(serviceInfo.wechat)"
              class="contact-action"
            >
              复制
            </el-button>
          </div>

          <!-- 邮箱联系 -->
          <div class="contact-item" v-if="serviceInfo.email">
            <div class="contact-icon email">📧</div>
            <div class="contact-info">
              <div class="contact-label">邮箱支持</div>
              <div class="contact-value">{{ serviceInfo.email }}</div>
              <div class="contact-desc">发送邮件咨询，我们会在24小时内回复</div>
            </div>
            <el-button 
              type="warning" 
              size="small" 
              @click="copyEmail(serviceInfo.email)"
              class="contact-action"
            >
              复制
            </el-button>
          </div>
        </div>

        <!-- 服务说明 -->
        <div class="service-note">
          <div class="note-icon">💡</div>
          <div class="note-text">
            <strong>温馨提示：</strong>
            如需技术支持、业务咨询或续费服务，请通过以上方式联系我们的专业客服团队。
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading-content">
        <el-icon class="is-loading" size="32"><Loading /></el-icon>
        <p>正在获取客服信息...</p>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { getCustomerServiceInfo } from '@/services/authApi'

interface ServiceInfo {
  hotline: string
  wechat: string
  email: string
}

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const serviceInfo = ref<ServiceInfo | null>(null)
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  visible.value = false
}

// 获取客服信息
const fetchServiceInfo = async () => {
  try {
    loading.value = true
    const response = await getCustomerServiceInfo()
    serviceInfo.value = response
  } catch (error) {
    console.error('获取客服信息失败:', error)
    ElMessage.error('获取客服信息失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 通用复制功能
const copyToClipboard = async (text: string, type: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success(`${type}已复制到剪贴板`)
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success(`${type}已复制到剪贴板`)
  }
}

// 复制电话号码
const copyPhone = (phone: string) => {
  copyToClipboard(phone, '客服热线')
}

// 复制微信号
const copyWechat = (wechat: string) => {
  copyToClipboard(wechat, '微信号')
}

// 复制邮箱地址
const copyEmail = (email: string) => {
  copyToClipboard(email, '邮箱地址')
}

// 监听对话框打开，获取客服信息
watch(visible, (newValue) => {
  if (newValue && !serviceInfo.value) {
    fetchServiceInfo()
  }
})
</script>

<style scoped>
.service-content {
  padding: 8px 0;
}

.service-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.service-avatar {
  margin-right: 16px;
}

.avatar-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.service-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.service-title p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.contact-list {
  space-y: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.contact-item:hover {
  background: #f0f2f5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 16px;
  flex-shrink: 0;
}

.contact-icon.phone {
  background: linear-gradient(135deg, #409EFF 0%, #66B1FF 100%);
  color: white;
}

.contact-icon.wechat {
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
  color: white;
}

.contact-icon.email {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 100%);
  color: white;
}

.contact-info {
  flex: 1;
}

.contact-label {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.contact-value {
  font-size: 16px;
  font-weight: 700;
  color: #409EFF;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
}

.contact-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.contact-action {
  margin-left: 12px;
  flex-shrink: 0;
}

.service-note {
  display: flex;
  align-items: flex-start;
  background: linear-gradient(135deg, #E1F3FF 0%, #F0F9FF 100%);
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #C6E2FF;
}

.note-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.note-text {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.note-text strong {
  color: #303133;
}

.loading-content {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.loading-content .el-icon {
  margin-bottom: 16px;
  color: #409EFF;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .contact-info {
    text-align: center;
  }
  
  .service-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}
</style>