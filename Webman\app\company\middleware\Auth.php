<?php
namespace app\company\middleware;

use app\model\CsCompany;
use support\Redis;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class Auth implements MiddlewareInterface
{
    public function process(Request $request, callable $handler) : Response
    {
        $token = $request->header('token','');
        if (!in_array($request->action, $this->noNeedLogin()) && (empty($token) || !Redis::exists('company:' . $token))) {
            return fail('登录信息异常，请重新登录', 1003);
        }
        $request->admin_id = 0;
        $request->role_id = 0;
        $request->company_id = 0;
        if (!empty($token) && !in_array($request->action, $this->noNeedLogin())) {
            $adminRoleId = Redis::get('company:' . $token);
            $adminRoleId = explode('-',$adminRoleId);
            if (count($adminRoleId) != 3) {
                return fail('登录信息异常，请重新登录', 1003);
            }
            $request->admin_id = $adminRoleId[0];
            $request->role_id = $adminRoleId[1];
            $request->company_id = $adminRoleId[2];
            // 判断$company是否处于有效期内，如果是，将信息缓存一天，不再查询数据库
            $cacheKey = 'company_expired_at:' . $request->company_id;
            $expiredAt = Redis::get($cacheKey);
            if (empty($expiredAt)) {
                $expiredAt = CsCompany::where(['id' => $request->company_id])->value('expired_at');
                Redis::setex($cacheKey, 86400, $expiredAt);
            }
            if ($expiredAt < date('Y-m-d')) {
                return fail('公司授权已过期，请联系管理员', 1004);
            }
        }
        // var_dump(Redis::keys('company:*'));
        return $handler($request);
    }

    /**
     * 管理后台除了登录接口，其他接口都需要验证token，所以只验证action即可
     * @return string[]
     */
    protected function noNeedLogin(): array
    {
        return [
            'register',
            'login',
            'getLoginPage',
            'savePwd',
            'getCustomerServiceInfo',
            'sendForgotPasswordSms',
            'verifyForgotPasswordSms',
            'resetPassword',
            'resendForgotPasswordSms',
            'sendRegisterSms',
            'getUserAgreement',
            'getPrivacyPolicy'
        ];
    }

}
