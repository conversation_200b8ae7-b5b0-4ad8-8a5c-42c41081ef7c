<?php

namespace app\company\controller;

use app\model\CsCompany;
use app\model\CsShop;
use app\model\CsOrder;
use app\model\CsOrderGoods;
use app\model\CsGoods;
use app\model\CsUser;
use app\model\CsInventory;
use app\model\CsPaymentMethod;
use support\Request;
use support\Response;
use support\Log;
use support\Db;

/**
 * 优化版Dashboard控制器
 * 使用company_id字段直接查询，提升性能
 * 注意：需要先执行数据库迁移添加company_id字段
 */
class DashboardOptimizedController
{
    /**
     * 获取仪表板概览数据（优化版）
     * @param Request $request
     * @return Response
     */
    public function overview(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            
            // 获取门店总数和在线门店数
            $totalShops = CsShop::where('company_id', $companyId)->count();
            $onlineShops = CsShop::where('company_id', $companyId)->where('status', 1)->count();
            
            // 获取本月新增门店数
            $newShopsThisMonth = CsShop::where('company_id', $companyId)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->count();
            
            // 使用company_id直接查询订单（性能优化）
            $todayRevenue = CsOrder::where('company_id', $companyId)
                ->whereDate('created_at', date('Y-m-d'))
                ->where('order_status', '>=', 2)
                ->sum('real_pay_money');
            
            // 获取今日订单数
            $todayOrders = CsOrder::where('company_id', $companyId)
                ->whereDate('created_at', date('Y-m-d'))
                ->count();
            
            // 获取成功订单数
            $successOrders = CsOrder::where('company_id', $companyId)
                ->whereDate('created_at', date('Y-m-d'))
                ->where('order_status', '>=', 2)
                ->count();
            
            // 获取活跃会员数（本月有消费的会员）
            $activeMembers = CsOrder::where('company_id', $companyId)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->where('user_id', '>', 0)
                ->distinct('user_id')
                ->count();
            
            return success([
                'totalShops' => $totalShops,
                'onlineShops' => $onlineShops,
                'newShopsThisMonth' => $newShopsThisMonth,
                'todayRevenue' => floatval($todayRevenue),
                'todayOrders' => $todayOrders,
                'successOrders' => $successOrders,
                'activeMembers' => $activeMembers
            ]);
        } catch (\Exception $e) {
            Log::error('获取仪表板概览数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取营业额趋势数据（优化版）
     * @param Request $request
     * @return Response
     */
    public function revenueTrend(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today');
            
            // 直接使用company_id查询，避免子查询
            $query = CsOrder::where('company_id', $companyId)->where('order_status', '>=', 2);
            
            $data = [];
            $xAxis = [];
            
            switch ($period) {
                case 'today':
                    // 按4小时间隔统计今日数据
                    for ($hour = 0; $hour < 24; $hour += 4) {
                        $startTime = date('Y-m-d') . ' ' . str_pad($hour, 2, '0', STR_PAD_LEFT) . ':00:00';
                        $endTime = date('Y-m-d') . ' ' . str_pad($hour + 4, 2, '0', STR_PAD_LEFT) . ':00:00';
                        $xAxis[] = str_pad($hour, 2, '0', STR_PAD_LEFT) . ':00';
                        
                        $revenue = (clone $query)
                            ->where('created_at', '>=', $startTime)
                            ->where('created_at', '<', $endTime)
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                    }
                    break;
                    
                case 'week':
                    // 按天统计本周数据
                    for ($i = 6; $i >= 0; $i--) {
                        $date = date('Y-m-d', strtotime("-{$i} days"));
                        $dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date('w', strtotime($date))];
                        $xAxis[] = $dayName;
                        
                        $revenue = (clone $query)
                            ->whereDate('created_at', $date)
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                    }
                    break;
                    
                case 'month':
                    // 按周统计本月数据
                    $startOfMonth = date('Y-m-01');
                    $endOfMonth = date('Y-m-t');
                    
                    $current = strtotime($startOfMonth);
                    $end = strtotime($endOfMonth);
                    
                    while ($current <= $end) {
                        $weekStart = $current;
                        $weekEnd = min(strtotime('+6 days', $current), $end);
                        
                        $xAxis[] = date('j', $weekStart) . '日';
                        
                        $revenue = (clone $query)
                            ->where('created_at', '>=', date('Y-m-d 00:00:00', $weekStart))
                            ->where('created_at', '<=', date('Y-m-d 23:59:59', $weekEnd))
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                        
                        $current = strtotime('+7 days', $current);
                    }
                    break;
                    
                case 'year':
                    // 按月统计本年数据
                    for ($month = 1; $month <= 12; $month++) {
                        $xAxis[] = $month . '月';
                        
                        $revenue = (clone $query)
                            ->whereMonth('created_at', $month)
                            ->whereYear('created_at', date('Y'))
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                    }
                    break;
            }
            
            return success([
                'period' => $period,
                'xAxis' => $xAxis,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取营业额趋势数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取门店业绩对比数据（优化版）
     * @param Request $request
     * @return Response
     */
    public function shopComparison(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today');
            
            $shops = CsShop::where('company_id', $companyId)
                ->where('status', 1)
                ->select('id', 'shop_name')
                ->get();
            
            if ($shops->isEmpty()) {
                return success([
                    'period' => $period,
                    'shopNames' => [],
                    'revenues' => []
                ]);
            }
            
            $shopNames = [];
            $revenues = [];
            
            foreach ($shops as $shop) {
                $shopNames[] = $shop->shop_name;
                
                // 使用company_id + shop_id的复合查询
                $query = CsOrder::where('company_id', $companyId)
                    ->where('shop_id', $shop->id)
                    ->where('order_status', '>=', 2);
                
                $this->applyPeriodFilter($query, $period);
                
                $revenue = $query->sum('real_pay_money');
                $revenues[] = floatval($revenue);
            }
            
            return success([
                'period' => $period,
                'shopNames' => $shopNames,
                'revenues' => $revenues
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店业绩对比数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取支付方式分析数据（优化版）
     * @param Request $request
     * @return Response
     */
    public function paymentAnalysis(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today');
            
            // 获取该企业下所有支付方式
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            if (empty($shopIds)) {
                return success([
                    'period' => $period,
                    'total_amount' => 0,
                    'payment_methods' => []
                ]);
            }
            
            // 获取所有支付方式
            $paymentMethods = CsPaymentMethod::whereIn('shop_id', $shopIds)
                ->where('status', 1)
                ->select('id', 'payment_method_name', 'shop_id')
                ->get()
                ->keyBy('id');
            
            // 使用company_id直接查询订单
            $query = CsOrder::where('company_id', $companyId)
                ->where('order_status', '>=', 2);
            $this->applyPeriodFilter($query, $period);
            
            $totalAmount = $query->sum('real_pay_money');
            
            if ($totalAmount == 0) {
                return success([
                    'period' => $period,
                    'total_amount' => 0,
                    'payment_methods' => []
                ]);
            }
            
            // 获取各支付方式的金额统计
            $paymentStats = (clone $query)
                ->selectRaw('payment_type, SUM(real_pay_money) as amount, COUNT(*) as count')
                ->groupBy('payment_type')
                ->get()
                ->keyBy('payment_type');
            
            // 构建返回数据
            $result = [];
            foreach ($paymentStats as $paymentTypeId => $stat) {
                $paymentMethod = $paymentMethods->get($paymentTypeId);
                $percentage = round(($stat->amount / $totalAmount) * 100, 1);
                
                $result[] = [
                    'id' => $paymentTypeId,
                    'name' => $paymentMethod ? $paymentMethod->payment_method_name : '支付方式' . $paymentTypeId,
                    'amount' => floatval($stat->amount),
                    'percentage' => $percentage,
                    'count' => intval($stat->count)
                ];
            }
            
            // 按金额排序
            usort($result, function($a, $b) {
                return $b['amount'] <=> $a['amount'];
            });
            
            return success([
                'period' => $period,
                'total_amount' => floatval($totalAmount),
                'payment_methods' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取支付方式分析数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 应用时间段过滤
     * @param $query
     * @param string $period
     * @param string $dateField
     */
    private function applyPeriodFilter($query, string $period, string $dateField = 'created_at'): void
    {
        switch ($period) {
            case 'today':
                $query->whereDate($dateField, date('Y-m-d'));
                break;
            case 'week':
                $query->where($dateField, '>=', date('Y-m-d 00:00:00', strtotime('-6 days')));
                break;
            case 'month':
                $query->whereMonth($dateField, date('m'))
                      ->whereYear($dateField, date('Y'));
                break;
            case 'year':
                $query->whereYear($dateField, date('Y'));
                break;
        }
    }
}
