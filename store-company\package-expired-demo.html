<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套餐过期页面演示 - 授权企业管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #0D1B2A;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #1B365D 0%, #415A77 100%);
            color: white;
            padding: 32px;
            text-align: center;
        }
        
        .demo-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
        }
        
        .demo-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        
        .demo-content {
            padding: 32px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #e4e7ed;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #415A77 0%, #778DA9 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin-bottom: 16px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #0D1B2A;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            color: #778DA9;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .demo-actions {
            text-align: center;
            padding: 32px;
            border-top: 1px solid #e4e7ed;
            background: #fafbfc;
        }
        
        .btn-demo {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #415A77 0%, #1B365D 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(65, 90, 119, 0.3);
        }
        
        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(65, 90, 119, 0.4);
        }
        
        .tech-stack {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .tech-title {
            font-size: 18px;
            font-weight: 600;
            color: #0D1B2A;
            margin-bottom: 16px;
        }
        
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .tech-tag {
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            color: #415A77;
            font-weight: 500;
        }
        
        .preview-section {
            margin-bottom: 32px;
        }
        
        .preview-title {
            font-size: 20px;
            font-weight: 600;
            color: #0D1B2A;
            margin-bottom: 16px;
        }
        
        .preview-image {
            width: 100%;
            border: 1px solid #e4e7ed;
            border-radius: 12px;
            background: linear-gradient(135deg, #0D1B2A 0%, #1B365D 50%, #415A77 100%);
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            position: relative;
            overflow: hidden;
        }

        .preview-placeholder {
            text-align: center;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .preview-content {
            text-align: left;
        }

        .preview-icon {
            font-size: 80px;
            opacity: 0.8;
        }

        .preview-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            color: white;
        }

        .preview-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 16px;
        }

        .preview-btn {
            background: linear-gradient(135deg, #1B365D, #0D1B2A);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 24px;
            font-size: 14px;
            font-weight: 600;
        }

        .preview-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(ellipse at 25% 20%, rgba(232, 184, 109, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 75% 80%, rgba(199, 210, 221, 0.12) 0%, transparent 60%);
        }
        
        @media (max-width: 768px) {
            .demo-container {
                margin: 0;
                border-radius: 0;
            }

            .demo-header,
            .demo-content,
            .demo-actions {
                padding: 24px 20px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .tech-list {
                justify-content: center;
            }

            .preview-placeholder {
                flex-direction: column;
                gap: 20px;
            }

            .preview-content {
                text-align: center;
            }

            .preview-icon {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">套餐过期提示页面</h1>
            <p class="demo-subtitle">简洁明了的套餐过期提示页面，参考主流设计风格</p>
        </div>
        
        <div class="demo-content">
            <div class="tech-stack">
                <h3 class="tech-title">技术栈</h3>
                <div class="tech-list">
                    <span class="tech-tag">Vue 3</span>
                    <span class="tech-tag">TypeScript</span>
                    <span class="tech-tag">Element Plus</span>
                    <span class="tech-tag">SCSS</span>
                    <span class="tech-tag">商务蓝色主题</span>
                    <span class="tech-tag">响应式设计</span>
                </div>
            </div>
            
            <div class="preview-section">
                <h3 class="preview-title">页面预览</h3>
                <div class="preview-image">
                    <div class="preview-placeholder">
                        <div class="preview-content">
                            <div class="preview-title">您的试用已到期</div>
                            <div class="preview-subtitle">为避免影响正常使用，<br>请及时联系客服购买正式套餐</div>
                            <button class="preview-btn">重新登录</button>
                        </div>
                        <div class="preview-icon">📢</div>
                    </div>
                </div>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h4 class="feature-title">商务典雅设计</h4>
                    <p class="feature-desc">采用项目统一的商务蓝色主题，玻璃拟态效果，专业大气</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h4 class="feature-title">响应式布局</h4>
                    <p class="feature-desc">完美适配桌面端和移动端，确保在各种设备上都有良好的用户体验</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📢</div>
                    <h4 class="feature-title">精美的喇叭图标</h4>
                    <p class="feature-desc">使用项目主题色绘制的3D喇叭图标，配合声波动画，风格统一</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔗</div>
                    <h4 class="feature-title">清晰的操作引导</h4>
                    <p class="feature-desc">明确的文字说明和操作按钮，引导用户进行下一步操作</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h4 class="feature-title">轻量级动画</h4>
                    <p class="feature-desc">适度的动画效果，提升用户体验而不会过于花哨</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h4 class="feature-title">重点突出</h4>
                    <p class="feature-desc">重要信息突出显示，用户能够快速理解当前状态和需要采取的行动</p>
                </div>
            </div>
        </div>
        
        <div class="demo-actions">
            <a href="/package-expired" class="btn-demo">查看完整页面</a>
        </div>
    </div>
</body>
</html>
