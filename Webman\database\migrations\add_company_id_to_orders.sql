-- 为订单表添加company_id字段以优化查询性能
-- 执行前请备份数据库

-- 1. 为cs_orders表添加company_id字段
ALTER TABLE `cs_orders` ADD COLUMN `company_id` INT(11) NOT NULL DEFAULT 0 COMMENT '所属企业ID' AFTER `shop_id`;

-- 2. 为cs_order_goodss表添加company_id字段  
ALTER TABLE `cs_order_goodss` ADD COLUMN `company_id` INT(11) NOT NULL DEFAULT 0 COMMENT '所属企业ID' AFTER `shop_id`;

-- 3. 更新cs_orders表的company_id字段
UPDATE `cs_orders` o 
INNER JOIN `cs_shops` s ON o.shop_id = s.id 
SET o.company_id = s.company_id;

-- 4. 更新cs_order_goodss表的company_id字段
UPDATE `cs_order_goodss` og 
INNER JOIN `cs_shops` s ON og.shop_id = s.id 
SET og.company_id = s.company_id;

-- 5. 为company_id字段添加索引以提升查询性能
ALTER TABLE `cs_orders` ADD INDEX `idx_company_id` (`company_id`);
ALTER TABLE `cs_orders` ADD INDEX `idx_company_created` (`company_id`, `created_at`);
ALTER TABLE `cs_orders` ADD INDEX `idx_company_status_created` (`company_id`, `order_status`, `created_at`);

ALTER TABLE `cs_order_goodss` ADD INDEX `idx_company_id` (`company_id`);
ALTER TABLE `cs_order_goodss` ADD INDEX `idx_company_created` (`company_id`, `created_at`);

-- 6. 为支付方式查询添加复合索引
ALTER TABLE `cs_orders` ADD INDEX `idx_company_payment_created` (`company_id`, `payment_type`, `created_at`);

-- 注意：执行完成后需要更新对应的Model文件中的字段定义
