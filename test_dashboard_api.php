<?php
/**
 * Dashboard API 测试脚本
 * 用于测试新创建的Dashboard API接口
 */

require_once __DIR__ . '/Webman/vendor/autoload.php';

use Webman\App;
use Webman\Config;
use support\Request;

// 初始化Webman
Config::load(config_path(), ['route', 'container']);

// 模拟请求数据
$testCompanyId = 1; // 请根据实际情况修改

echo "=== Dashboard API 测试开始 ===\n\n";

// 测试1: 概览数据
echo "1. 测试概览数据 API\n";
try {
    $request = new Request();
    $request->company_id = $testCompanyId;
    
    $controller = new \app\company\controller\DashboardController();
    $response = $controller->overview($request);
    
    echo "✅ 概览数据 API 测试成功\n";
    echo "响应: " . $response->rawBody() . "\n\n";
} catch (Exception $e) {
    echo "❌ 概览数据 API 测试失败: " . $e->getMessage() . "\n\n";
}

// 测试2: 营业额趋势
echo "2. 测试营业额趋势 API\n";
try {
    $request = new Request();
    $request->company_id = $testCompanyId;
    $_GET['period'] = 'today';
    
    $controller = new \app\company\controller\DashboardController();
    $response = $controller->revenueTrend($request);
    
    echo "✅ 营业额趋势 API 测试成功\n";
    echo "响应: " . $response->rawBody() . "\n\n";
} catch (Exception $e) {
    echo "❌ 营业额趋势 API 测试失败: " . $e->getMessage() . "\n\n";
}

// 测试3: 门店对比
echo "3. 测试门店对比 API\n";
try {
    $request = new Request();
    $request->company_id = $testCompanyId;
    $_GET['period'] = 'today';
    
    $controller = new \app\company\controller\DashboardController();
    $response = $controller->shopComparison($request);
    
    echo "✅ 门店对比 API 测试成功\n";
    echo "响应: " . $response->rawBody() . "\n\n";
} catch (Exception $e) {
    echo "❌ 门店对比 API 测试失败: " . $e->getMessage() . "\n\n";
}

// 测试4: 支付方式分析
echo "4. 测试支付方式分析 API\n";
try {
    $request = new Request();
    $request->company_id = $testCompanyId;
    $_GET['period'] = 'today';
    
    $controller = new \app\company\controller\DashboardController();
    $response = $controller->paymentAnalysis($request);
    
    echo "✅ 支付方式分析 API 测试成功\n";
    echo "响应: " . $response->rawBody() . "\n\n";
} catch (Exception $e) {
    echo "❌ 支付方式分析 API 测试失败: " . $e->getMessage() . "\n\n";
}

// 测试5: 商品排行
echo "5. 测试商品排行 API\n";
try {
    $request = new Request();
    $request->company_id = $testCompanyId;
    $_GET['period'] = 'today';
    
    $controller = new \app\company\controller\DashboardController();
    $response = $controller->productRanking($request);
    
    echo "✅ 商品排行 API 测试成功\n";
    echo "响应: " . $response->rawBody() . "\n\n";
} catch (Exception $e) {
    echo "❌ 商品排行 API 测试失败: " . $e->getMessage() . "\n\n";
}

// 测试6: 门店状态
echo "6. 测试门店状态 API\n";
try {
    $request = new Request();
    $request->company_id = $testCompanyId;
    
    $controller = new \app\company\controller\DashboardController();
    $response = $controller->shopStatus($request);
    
    echo "✅ 门店状态 API 测试成功\n";
    echo "响应: " . $response->rawBody() . "\n\n";
} catch (Exception $e) {
    echo "❌ 门店状态 API 测试失败: " . $e->getMessage() . "\n\n";
}

echo "=== Dashboard API 测试完成 ===\n";
