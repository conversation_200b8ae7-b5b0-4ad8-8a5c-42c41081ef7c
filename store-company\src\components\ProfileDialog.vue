<template>
  <div v-if="visible" class="modal-overlay" @click.self="closeDialog">
    <div class="modal-dialog">
      <div class="modal-header">
        <h3 class="modal-title">个人中心</h3>
        <button class="modal-close" @click="closeDialog">×</button>
      </div>
      <div class="modal-body">
        <form class="profile-form">
          <!-- 第一行：所属角色 + 登录账号 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">所属角色</label>
              <input
                type="text"
                class="form-control readonly"
                v-model="profileForm.role_name"
                readonly
                placeholder="所属角色"
              >
            </div>

            <div class="form-group">
              <label class="form-label">登录账号</label>
              <input
                type="text"
                class="form-control readonly"
                v-model="profileForm.username"
                readonly
                placeholder="登录账号"
              >
            </div>
          </div>

          <!-- 第二行：昵称 + 登录密码 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">昵称 <span class="required">*</span></label>
              <input
                type="text"
                class="form-control"
                v-model="profileForm.nickname"
                placeholder="请输入昵称"
                :disabled="loading"
              >
            </div>

            <div class="form-group">
              <label class="form-label">登录密码</label>
              <div class="password-input-wrapper">
                <input
                  :type="showPassword ? 'text' : 'password'"
                  class="form-control"
                  v-model="profileForm.password"
                  placeholder="请输入新密码（留空则不修改）"
                  :disabled="loading"
                >
                <button
                  type="button"
                  class="password-toggle"
                  @click="showPassword = !showPassword"
                  :disabled="loading"
                >
                  {{ showPassword ? '👁️' : '👁️‍🗨️' }}
                </button>
              </div>
            </div>
          </div>

          <!-- 密码提示信息 -->
          <div class="form-hint-row">
            <div class="form-hint">为了安全，请输入新密码。如不需要修改密码，请留空。</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="closeDialog" :disabled="loading">取消</button>
        <button class="btn btn-primary" @click="saveProfile" :disabled="loading">
          <span v-if="loading">保存中...</span>
          <span v-else><i class="icon">💾</i> 保存修改</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 消息提示 -->
  <MessageToast ref="messageToastRef" />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import MessageToast from './MessageToast.vue'
import { useMessage } from '@/composables/useDialog'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['update:visible', 'profile-updated'])

// 使用auth store
const authStore = useAuthStore()

// 使用消息提示
const { setMessageToastRef, success, error } = useMessage()
const messageToastRef = ref<any>(null)

// 表单数据
const profileForm = reactive({
  id: null as number | null,
  nickname: '',
  username: '',
  password: '',
  role_name: ''
})

// 状态变量
const loading = ref(false)
const showPassword = ref(false)

// 初始化表单数据
const initForm = () => {
  if (authStore.user) {
    profileForm.id = authStore.user.id
    profileForm.nickname = authStore.user.nickname || ''
    profileForm.username = authStore.user.username || ''
    profileForm.password = '' // 密码字段为空，用户需要输入新密码
    profileForm.role_name = authStore.user.role_name || ''
  }
}

// 关闭对话框
const closeDialog = () => {
  emit('update:visible', false)
}

// 保存个人资料
const saveProfile = async () => {
  // 表单验证
  if (!profileForm.nickname.trim()) {
    error('请输入昵称')
    return
  }

  if (profileForm.nickname.length < 2 || profileForm.nickname.length > 20) {
    error('昵称长度应在2-20个字符之间')
    return
  }

  // 如果输入了密码，则验证密码长度
  if (profileForm.password.trim() && (profileForm.password.length < 6 || profileForm.password.length > 20)) {
    error('密码长度应在6-20个字符之间')
    return
  }
  
  loading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 构建要保存的数据
    const saveData: any = {
      id: profileForm.id,
      nickname: profileForm.nickname
    }

    // 只有输入了密码才包含密码字段
    if (profileForm.password.trim()) {
      saveData.password = profileForm.password
    }

    // 这里应该调用实际的API
    console.log('保存个人资料:', saveData)
    
    // 更新store中的用户信息
    if (authStore.user) {
      authStore.user.nickname = profileForm.nickname
    }

    // 触发成功事件
    emit('profile-updated', {
      nickname: profileForm.nickname,
      username: profileForm.username
    })

    success('个人资料修改成功')
    closeDialog()
  } catch (err) {
    console.error('保存失败:', err)
    error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

onMounted(() => {
  initForm()
  setMessageToastRef(messageToastRef.value)
})
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-deep: rgba(13, 27, 42, 0.25);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 650px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表单样式 */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 表单行布局 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  width: 100%;
}

.form-label {
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.required {
  color: #dc3545;
}

.form-control {
  padding: 14px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: white;
  min-height: 44px;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
  box-shadow: 0 0 0 2px rgba(65, 90, 119, 0.1);
}

.form-control.readonly {
  background: #f8f9fa;
  color: var(--text-muted);
  cursor: not-allowed;
}

.form-control:disabled {
  background: #f8f9fa;
  color: var(--text-muted);
  cursor: not-allowed;
}

/* 密码输入框样式 */
.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-wrapper .form-control {
  padding-right: 48px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-muted);
  font-size: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: var(--text-secondary);
  background: var(--secondary-light-blue-gray);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-steel-blue);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-business-blue);
}

.btn-secondary {
  background: var(--secondary-elegant-blue);
  color: var(--text-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-steel-blue);
  color: white;
}

.icon {
  font-size: 14px;
}

/* 表单提示文字 */
.form-hint {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 4px;
  line-height: 1.4;
}

.form-hint-row {
  margin-top: -12px;
  margin-bottom: 8px;
}

.form-hint-row .form-hint {
  margin-top: 0;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .modal-dialog {
    max-width: 90%;
    width: 90%;
    margin: 16px;
  }
}

@media (max-width: 768px) {
  .modal-dialog {
    width: 95%;
    margin: 16px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .profile-form {
    gap: 16px;
  }

  /* 在小屏幕上改为单列布局 */
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
