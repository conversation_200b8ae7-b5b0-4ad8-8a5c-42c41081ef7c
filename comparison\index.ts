// 同比/环比数据对比组件库入口文件

import ComparisonToggle from './ComparisonToggle.vue';
import EnhancedStatCard from './EnhancedStatCard.vue';
import ChartComparisonPanel from './ChartComparisonPanel.vue';

// 类型定义导出
export interface ComparisonData {
  percent: number;
  absolute: number;
  trend: 'up' | 'down' | 'equal';
  consecutivePeriods?: number;
  additionalInfo?: string;
}

export interface StatData {
  id: string;
  title: string;
  value: number | string;
  icon: string;
  iconColor: 'blue' | 'green' | 'orange' | 'purple';
  comparison?: ComparisonData;
  secondaryMetric?: {
    label: string;
    value: string;
    comparison?: ComparisonData;
  };
}

export interface ComparisonMetric {
  title: string;
  value: number | string;
  change: ComparisonData;
}

export interface PeriodOption {
  value: string;
  label: string;
}

// 组件导出
export {
  ComparisonToggle,
  EnhancedStatCard,
  ChartComparisonPanel
};

// 默认导出主组件
export { default as ComparisonSystem } from '../ComparisonComponents.vue';

// 工具函数导出
export const comparisonUtils = {
  /**
   * 格式化百分比显示
   */
  formatPercentage: (value: number): string => {
    return value > 0 ? `+${value.toFixed(1)}` : value.toFixed(1);
  },

  /**
   * 获取趋势类名
   */
  getTrendClass: (trend: string): string => {
    switch (trend) {
      case 'up':
        return 'positive';
      case 'down':
        return 'negative';
      default:
        return 'neutral';
    }
  },

  /**
   * 获取趋势箭头
   */
  getTrendArrow: (trend: string): string => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      default:
        return '→';
    }
  },

  /**
   * 格式化数值显示
   */
  formatNumber: (num: number): string => {
    if (num > 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    }
    return num.toLocaleString('zh-CN');
  },

  /**
   * 计算对比期间的值
   */
  calculateBasePeriodValue: (currentValue: number, comparison: ComparisonData): number => {
    return currentValue - comparison.absolute;
  },

  /**
   * 生成期间选项
   */
  generatePeriodOptions: (mode: 'yoy' | 'mom'): PeriodOption[] => {
    if (mode === 'yoy') {
      return [
        { value: 'last_year_today', label: '去年同日' },
        { value: 'last_year_week', label: '去年同周' },
        { value: 'last_year_month', label: '去年同月' },
        { value: 'last_year_quarter', label: '去年同季度' },
        { value: 'last_year_year', label: '去年同期' }
      ];
    } else {
      return [
        { value: 'yesterday', label: '昨日' },
        { value: 'last_week', label: '上周' },
        { value: 'last_month', label: '上月' },
        { value: 'last_quarter', label: '上季度' },
        { value: 'last_half_year', label: '上半年' }
      ];
    }
  },

  /**
   * 获取期间描述文本
   */
  getPeriodText: (period: string): string => {
    const periodMap: Record<string, string> = {
      'yesterday': '昨日',
      'last_week': '上周',
      'last_month': '上月',
      'last_quarter': '上季度',
      'last_half_year': '上半年',
      'last_year_today': '去年同日',
      'last_year_week': '去年同周',
      'last_year_month': '去年同月',
      'last_year_quarter': '去年同季度',
      'last_year_year': '去年同期'
    };
    return periodMap[period] || '上期';
  },

  /**
   * 生成模拟趋势数据（用于演示）
   */
  generateMockTrendData: (trend: 'up' | 'down' | 'equal', points: number = 7): number[] => {
    const baseValue = 100;
    const data = [baseValue];
    
    for (let i = 1; i < points; i++) {
      let change;
      if (trend === 'up') {
        change = Math.random() * 10 + 2;
      } else if (trend === 'down') {
        change = -(Math.random() * 10 + 2);
      } else {
        change = (Math.random() - 0.5) * 4;
      }
      data.push(data[i - 1] + change);
    }
    
    return data;
  },

  /**
   * 生成日期标签
   */
  generateDateLabels: (days: number = 7): string[] => {
    const labels = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
    }
    
    return labels;
  },

  /**
   * 导出数据为CSV格式
   */
  exportToCSV: (metrics: ComparisonMetric[], mode: 'yoy' | 'mom'): string => {
    const headers = ['指标名称', '当前值', '变化百分比', '绝对变化', '趋势'];
    const rows = metrics.map(metric => [
      metric.title,
      metric.value,
      `${metric.change.percent}%`,
      metric.change.absolute,
      metric.change.trend === 'up' ? '上升' : metric.change.trend === 'down' ? '下降' : '持平'
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  },

  /**
   * 下载CSV文件
   */
  downloadCSV: (content: string, filename: string): void => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

// 默认配置
export const defaultComparisonConfig = {
  // 默认对比模式
  defaultMode: 'yoy' as 'yoy' | 'mom',
  
  // 自动刷新间隔（毫秒）
  autoRefreshInterval: 30000,
  
  // 动画持续时间
  animationDuration: 300,
  
  // 数字动画配置
  countUpOptions: {
    duration: 2,
    useEasing: true,
    useGrouping: true,
    separator: ',',
    decimal: '.'
  },
  
  // 图表颜色配置
  chartColors: {
    positive: '#67C23A',
    negative: '#F56C6C',
    neutral: '#909399',
    primary: '#1B365D'
  },
  
  // 响应式断点
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  }
};

// 版本信息
export const version = '1.0.0';