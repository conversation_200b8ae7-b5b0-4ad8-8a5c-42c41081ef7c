<template>
  <div class="chart-comparison-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">数据对比分析</div>
      <div class="panel-controls">
        <!-- 对比模式切换 -->
        <div class="comparison-toggle mini">
          <button 
            class="comparison-toggle-btn"
            :class="{ active: comparisonMode === 'yoy' }"
            @click="handleModeChange('yoy')"
          >
            同比
          </button>
          <button 
            class="comparison-toggle-btn"
            :class="{ active: comparisonMode === 'mom' }"
            @click="handleModeChange('mom')"
          >
            环比
          </button>
        </div>
        
        <!-- 导出按钮 -->
        <button class="export-btn" @click="exportData" :disabled="loading">
          <span class="export-icon">📊</span>
          导出
        </button>
      </div>
    </div>

    <!-- 指标概览 -->
    <div class="metrics-overview" v-if="!loading">
      <div class="overview-item" v-for="summary in metricsSummary" :key="summary.type">
        <div class="overview-icon" :class="summary.trend">
          {{ summary.icon }}
        </div>
        <div class="overview-content">
          <div class="overview-title">{{ summary.title }}</div>
          <div class="overview-value" :class="summary.trend">
            {{ summary.count }}项指标{{ summary.trend === 'positive' ? '上升' : summary.trend === 'negative' ? '下降' : '持平' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 对比指标网格 -->
    <div class="comparison-metrics" v-if="!loading">
      <div 
        class="metric-item"
        v-for="(metric, index) in metrics"
        :key="metric.title"
        :style="{ animationDelay: `${index * 0.1}s` }"
        @click="handleMetricClick(metric)"
      >
        <div class="metric-header">
          <div class="metric-title">{{ metric.title }}</div>
          <div class="metric-trend-indicator" :class="getTrendClass(metric.change.trend)">
            <span class="trend-arrow">{{ getTrendArrow(metric.change.trend) }}</span>
          </div>
        </div>
        
        <div class="metric-value">{{ formatMetricValue(metric.value) }}</div>
        
        <div class="metric-change" :class="getTrendClass(metric.change.trend)">
          <span class="change-percentage">
            {{ formatPercentage(metric.change.percent) }}%
          </span>
          <span class="change-label">{{ comparisonModeText }}</span>
        </div>
        
        <div class="metric-detail">
          <span class="detail-text">
            {{ metric.change.trend === 'up' ? '增长' : metric.change.trend === 'down' ? '减少' : '持平' }}
            {{ formatAbsoluteValue(metric.change.absolute) }}
          </span>
        </div>

        <!-- 迷你图表 -->
        <div class="mini-chart" :ref="el => setChartRef(el, `chart-${index}`)"></div>
      </div>
    </div>

    <!-- 详细分析展开区域 -->
    <div class="detailed-analysis" v-if="selectedMetric && showDetailedAnalysis">
      <div class="analysis-header">
        <h4>{{ selectedMetric.title }} - 详细分析</h4>
        <button class="close-analysis" @click="closeDetailedAnalysis">×</button>
      </div>
      
      <div class="analysis-content">
        <div class="analysis-charts">
          <div class="trend-chart" ref="trendChartRef"></div>
          <div class="comparison-chart" ref="comparisonChartRef"></div>
        </div>
        
        <div class="analysis-insights">
          <div class="insight-item" v-for="insight in getInsights(selectedMetric)" :key="insight.type">
            <div class="insight-icon">{{ insight.icon }}</div>
            <div class="insight-content">
              <div class="insight-title">{{ insight.title }}</div>
              <div class="insight-description">{{ insight.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="comparison-loading" v-if="loading">
      <div class="loading-spinner"></div>
      正在计算对比数据...
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted, defineProps, defineEmits } from 'vue';
import * as echarts from 'echarts';

interface ComparisonData {
  percent: number;
  absolute: number;
  trend: 'up' | 'down' | 'equal';
}

interface ComparisonMetric {
  title: string;
  value: number | string;
  change: ComparisonData;
}

interface Props {
  comparisonMode: 'yoy' | 'mom';
  metrics: ComparisonMetric[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<{
  'mode-change': [mode: 'yoy' | 'mom'];
  'export': [data: any];
  'metric-click': [metric: ComparisonMetric];
}>();

// 响应式状态
const selectedMetric = ref<ComparisonMetric | null>(null);
const showDetailedAnalysis = ref(false);
const chartRefs = reactive<Record<string, HTMLElement>>({});
const trendChartRef = ref<HTMLElement>();
const comparisonChartRef = ref<HTMLElement>();
const chartInstances = reactive<Record<string, echarts.ECharts>>({});

// 计算属性
const comparisonModeText = computed(() => {
  return props.comparisonMode === 'yoy' ? '同比' : '环比';
});

const metricsSummary = computed(() => {
  const positive = props.metrics.filter(m => m.change.trend === 'up').length;
  const negative = props.metrics.filter(m => m.change.trend === 'down').length;
  const neutral = props.metrics.filter(m => m.change.trend === 'equal').length;

  return [
    {
      type: 'positive',
      title: '上升指标',
      count: positive,
      icon: '📈',
      trend: 'positive'
    },
    {
      type: 'negative', 
      title: '下降指标',
      count: negative,
      icon: '📉',
      trend: 'negative'
    },
    {
      type: 'neutral',
      title: '持平指标',
      count: neutral,
      icon: '➖',
      trend: 'neutral'
    }
  ].filter(item => item.count > 0);
});

// 方法
const handleModeChange = (mode: 'yoy' | 'mom') => {
  emit('mode-change', mode);
};

const getTrendClass = (trend: string) => {
  switch (trend) {
    case 'up':
      return 'positive';
    case 'down':
      return 'negative';
    default:
      return 'neutral';
  }
};

const getTrendArrow = (trend: string) => {
  switch (trend) {
    case 'up':
      return '↗';
    case 'down':
      return '↘';
    default:
      return '→';
  }
};

const formatPercentage = (value: number) => {
  return value > 0 ? `+${value.toFixed(1)}` : value.toFixed(1);
};

const formatMetricValue = (value: number | string) => {
  if (typeof value === 'number') {
    if (value > 10000) {
      return `${(value / 10000).toFixed(1)}万`;
    }
    return value.toLocaleString();
  }
  return value;
};

const formatAbsoluteValue = (value: number) => {
  if (Math.abs(value) > 10000) {
    return `${(Math.abs(value) / 10000).toFixed(1)}万`;
  }
  return Math.abs(value).toLocaleString();
};

const handleMetricClick = (metric: ComparisonMetric) => {
  selectedMetric.value = metric;
  showDetailedAnalysis.value = true;
  
  nextTick(() => {
    initDetailedCharts();
  });
  
  emit('metric-click', metric);
};

const closeDetailedAnalysis = () => {
  showDetailedAnalysis.value = false;
  selectedMetric.value = null;
};

const exportData = () => {
  const exportData = {
    mode: props.comparisonMode,
    metrics: props.metrics,
    summary: metricsSummary.value,
    timestamp: new Date().toISOString()
  };
  
  emit('export', exportData);
  
  // 创建并下载CSV文件
  const csvContent = generateCSV(exportData);
  downloadCSV(csvContent, `comparison-data-${props.comparisonMode}-${Date.now()}.csv`);
};

const generateCSV = (data: any) => {
  const headers = ['指标名称', '当前值', '变化百分比', '绝对变化', '趋势'];
  const rows = data.metrics.map((metric: ComparisonMetric) => [
    metric.title,
    metric.value,
    `${metric.change.percent}%`,
    metric.change.absolute,
    metric.change.trend === 'up' ? '上升' : metric.change.trend === 'down' ? '下降' : '持平'
  ]);
  
  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const setChartRef = (el: HTMLElement | null, key: string) => {
  if (el) {
    chartRefs[key] = el;
  }
};

const initMiniCharts = () => {
  props.metrics.forEach((metric, index) => {
    const chartEl = chartRefs[`chart-${index}`];
    if (chartEl) {
      const chart = echarts.init(chartEl);
      
      // 生成模拟趋势数据
      const trendData = generateTrendData(metric.change.trend);
      
      const option = {
        grid: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        },
        xAxis: {
          type: 'category',
          show: false,
          data: Array.from({ length: 7 }, (_, i) => i)
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [{
          type: 'line',
          data: trendData,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: metric.change.trend === 'up' ? '#67C23A' : 
                   metric.change.trend === 'down' ? '#F56C6C' : '#909399'
          },
          areaStyle: {
            opacity: 0.3,
            color: metric.change.trend === 'up' ? '#67C23A' : 
                   metric.change.trend === 'down' ? '#F56C6C' : '#909399'
          }
        }]
      };
      
      chart.setOption(option);
      chartInstances[`chart-${index}`] = chart;
    }
  });
};

const initDetailedCharts = () => {
  if (trendChartRef.value && selectedMetric.value) {
    const trendChart = echarts.init(trendChartRef.value);
    
    // 趋势图配置
    const trendOption = {
      title: {
        text: '趋势变化',
        textStyle: {
          fontSize: 14,
          color: '#415A77'
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: generateDateLabels()
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: selectedMetric.value.title,
        type: 'line',
        data: generateDetailedTrendData(selectedMetric.value.change.trend),
        smooth: true,
        lineStyle: {
          width: 3,
          color: selectedMetric.value.change.trend === 'up' ? '#67C23A' : 
                 selectedMetric.value.change.trend === 'down' ? '#F56C6C' : '#909399'
        },
        areaStyle: {
          opacity: 0.2
        }
      }]
    };
    
    trendChart.setOption(trendOption);
  }
  
  if (comparisonChartRef.value && selectedMetric.value) {
    const comparisonChart = echarts.init(comparisonChartRef.value);
    
    // 对比图配置
    const comparisonOption = {
      title: {
        text: '对比分析',
        textStyle: {
          fontSize: 14,
          color: '#415A77'
        }
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [{
        name: '对比分析',
        type: 'pie',
        radius: '50%',
        data: [
          {
            value: Math.abs(selectedMetric.value.change.absolute),
            name: selectedMetric.value.change.trend === 'up' ? '增长部分' : '减少部分',
            itemStyle: {
              color: selectedMetric.value.change.trend === 'up' ? '#67C23A' : '#F56C6C'
            }
          },
          {
            value: typeof selectedMetric.value.value === 'number' ? 
                   selectedMetric.value.value - Math.abs(selectedMetric.value.change.absolute) :
                   1000,
            name: '基础部分',
            itemStyle: {
              color: '#E0E1DD'
            }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };
    
    comparisonChart.setOption(comparisonOption);
  }
};

const generateTrendData = (trend: string) => {
  const baseValue = 100;
  const data = [baseValue];
  
  for (let i = 1; i < 7; i++) {
    let change;
    if (trend === 'up') {
      change = Math.random() * 10 + 5;
    } else if (trend === 'down') {
      change = -(Math.random() * 10 + 5);
    } else {
      change = (Math.random() - 0.5) * 4;
    }
    data.push(data[i - 1] + change);
  }
  
  return data;
};

const generateDetailedTrendData = (trend: string) => {
  return generateTrendData(trend);
};

const generateDateLabels = () => {
  const labels = [];
  const today = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
  }
  
  return labels;
};

const getInsights = (metric: ComparisonMetric) => {
  const insights = [];
  
  if (metric.change.trend === 'up') {
    insights.push({
      type: 'positive',
      icon: '🎯',
      title: '表现优秀',
      description: `${metric.title}呈现良好的增长趋势，超出预期表现`
    });
    
    if (metric.change.percent > 20) {
      insights.push({
        type: 'highlight',
        icon: '🚀',
        title: '强劲增长',
        description: '增长幅度超过20%，属于高速增长区间'
      });
    }
  } else if (metric.change.trend === 'down') {
    insights.push({
      type: 'warning',
      icon: '⚠️',
      title: '需要关注',
      description: `${metric.title}出现下降趋势，建议分析原因并制定改进措施`
    });
    
    if (metric.change.percent < -10) {
      insights.push({
        type: 'alert',
        icon: '🔴',
        title: '显著下降',
        description: '下降幅度较大，需要立即采取行动'
      });
    }
  } else {
    insights.push({
      type: 'neutral',
      icon: '📊',
      title: '稳定表现',
      description: `${metric.title}保持稳定，与上期基本持平`
    });
  }
  
  return insights;
};

// 监听器
watch(() => props.metrics, () => {
  nextTick(() => {
    initMiniCharts();
  });
}, { immediate: true });

// 生命周期
onMounted(() => {
  nextTick(() => {
    initMiniCharts();
  });
});
</script>

<style scoped>
.chart-comparison-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-light);
  border: 1px solid #e4e7ed;
  margin-top: 16px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-title::before {
  content: '📊';
  font-size: 20px;
}

.panel-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comparison-toggle.mini {
  margin: 0;
}

.comparison-toggle.mini .comparison-toggle-btn {
  padding: 6px 12px;
  font-size: 12px;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: var(--secondary-light-blue-gray);
  border: 1px solid var(--accent-platinum);
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.export-btn:hover:not(:disabled) {
  background: var(--accent-platinum);
  color: var(--primary-business-blue);
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-icon {
  font-size: 14px;
}

.metrics-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(240,248,255,0.5), rgba(248,250,252,0.8));
  border-radius: 8px;
  border: 1px solid rgba(27, 54, 93, 0.06);
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.overview-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.overview-icon.positive {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(103, 194, 58, 0.2));
}

.overview-icon.negative {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.1), rgba(245, 108, 108, 0.2));
}

.overview-icon.neutral {
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.1), rgba(144, 147, 153, 0.2));
}

.overview-content {
  flex: 1;
}

.overview-title {
  font-size: 12px;
  color: var(--text-light);
  margin-bottom: 2px;
}

.overview-value {
  font-size: 14px;
  font-weight: 600;
}

.overview-value.positive {
  color: var(--success-color);
}

.overview-value.negative {
  color: var(--danger-color);
}

.overview-value.neutral {
  color: var(--info-color);
}

.comparison-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.metric-item {
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,250,252,0.8));
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(27, 54, 93, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  animation: slideInUp 0.3s ease-out both;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(27, 54, 93, 0.1);
  border-color: var(--primary-business-blue);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.metric-title {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.metric-trend-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.metric-trend-indicator.positive {
  background: var(--success-color);
}

.metric-trend-indicator.negative {
  background: var(--danger-color);
}

.metric-trend-indicator.neutral {
  background: var(--info-color);
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 8px;
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--danger-color);
}

.metric-change.neutral {
  color: var(--info-color);
}

.change-label {
  font-size: 11px;
  opacity: 0.8;
}

.metric-detail {
  font-size: 12px;
  color: var(--text-light);
  margin-bottom: 12px;
}

.mini-chart {
  height: 40px;
  width: 100%;
}

.detailed-analysis {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(240,248,255,0.3), rgba(248,250,252,0.6));
  border-radius: 8px;
  border: 1px solid rgba(27, 54, 93, 0.1);
}

.analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.analysis-header h4 {
  margin: 0;
  font-size: 16px;
  color: var(--text-primary);
}

.close-analysis {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(245, 108, 108, 0.1);
  color: var(--danger-color);
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.close-analysis:hover {
  background: var(--danger-color);
  color: white;
}

.analysis-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.analysis-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.trend-chart,
.comparison-chart {
  height: 200px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.analysis-insights {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.insight-icon {
  font-size: 16px;
  margin-top: 2px;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.insight-description {
  font-size: 12px;
  color: var(--text-light);
  line-height: 1.4;
}

.comparison-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-muted);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--accent-platinum);
  border-top: 2px solid var(--primary-business-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .analysis-content {
    grid-template-columns: 1fr;
  }
  
  .analysis-charts {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .comparison-metrics {
    grid-template-columns: 1fr;
  }
  
  .metrics-overview {
    grid-template-columns: 1fr;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .panel-controls {
    justify-content: center;
  }
  
  .metric-item {
    padding: 16px;
  }
}
</style>