<?php

namespace app\company\controller;

use app\model\CsShop;
use app\model\CsOrder;
use app\model\CsOrderGoods;
use app\model\CsGoods;
use app\model\CsUser;
use app\model\CsTable;
use support\Request;
use support\Response;
use support\Log;
use support\Db;

class CsDataAggregationController
{
    /**
     * 获取实时业务指标汇总
     * @param Request $request
     * @return Response
     */
    public function realtimeSummary(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            
            // 今日数据汇总
            $todaySummary = $this->getTodaySummary($companyId);
            
            // 本月数据汇总
            $monthSummary = $this->getMonthSummary($companyId);
            
            // 门店状态汇总
            $shopSummary = $this->getShopSummary($companyId);
            
            // 实时桌台状态
            $tableSummary = $this->getTableSummary($companyId);

            return success([
                'today' => $todaySummary,
                'month' => $monthSummary,
                'shops' => $shopSummary,
                'tables' => $tableSummary,
                'timestamp' => time()
            ]);
        } catch (\Exception $e) {
            Log::error('获取实时业务指标汇总失败: ' . $e->getMessage());
            return fail('获取数据失败');
        }
    }

    /**
     * 获取营收趋势数据
     * @param Request $request
     * @return Response
     */
    public function revenueTrend(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'week'); // week, month, quarter
            $shopId = $request->get('shop_id', 0); // 0表示所有门店
            
            $trendData = $this->getRevenueTrendData($companyId, $period, $shopId);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'trend_data' => $trendData
            ]);
        } catch (\Exception $e) {
            Log::error('获取营收趋势数据失败: ' . $e->getMessage());
            return fail('获取营收趋势数据失败');
        }
    }

    /**
     * 获取门店业绩排行
     * @param Request $request
     * @return Response
     */
    public function shopRanking(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month'); // today, week, month
            $limit = $request->get('limit', 10);
            
            $rankingData = $this->getShopRankingData($companyId, $period, $limit);
            
            return success([
                'period' => $period,
                'ranking' => $rankingData
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店排行失败: ' . $e->getMessage());
            return fail('获取门店排行失败');
        }
    }

    /**
     * 获取商品销售排行
     * @param Request $request
     * @return Response
     */
    public function goodsRanking(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month'); // today, week, month
            $shopId = $request->get('shop_id', 0); // 0表示所有门店
            $limit = $request->get('limit', 20);
            
            $rankingData = $this->getGoodsRankingData($companyId, $period, $shopId, $limit);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'ranking' => $rankingData
            ]);
        } catch (\Exception $e) {
            Log::error('获取商品排行失败: ' . $e->getMessage());
            return fail('获取商品排行失败');
        }
    }

    /**
     * 获取客户分析数据
     * @param Request $request
     * @return Response
     */
    public function customerAnalysis(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'month');
            $shopId = $request->get('shop_id', 0);
            
            $customerData = $this->getCustomerAnalysisData($companyId, $period, $shopId);
            
            return success([
                'period' => $period,
                'shop_id' => $shopId,
                'customer_analysis' => $customerData
            ]);
        } catch (\Exception $e) {
            Log::error('获取客户分析数据失败: ' . $e->getMessage());
            return fail('获取客户分析数据失败');
        }
    }

    /**
     * 获取今日数据汇总
     * @param int $companyId
     * @return array
     */
    private function getTodaySummary(int $companyId): array
    {
        try {
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $todayData = CsOrder::whereIn('shop_id', $shopIds)
                ->whereDate('created_at', date('Y-m-d'))
                ->selectRaw('
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as avg_order_amount,
                    COUNT(DISTINCT user_id) as unique_customers
                ')
                ->first();

            return [
                'orders' => $todayData->total_orders ?? 0,
                'revenue' => $todayData->total_revenue ?? 0,
                'avg_order_amount' => $todayData->avg_order_amount ?? 0,
                'customers' => $todayData->unique_customers ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('获取今日汇总数据失败: ' . $e->getMessage());
            return ['orders' => 0, 'revenue' => 0, 'avg_order_amount' => 0, 'customers' => 0];
        }
    }

    /**
     * 获取本月数据汇总
     * @param int $companyId
     * @return array
     */
    private function getMonthSummary(int $companyId): array
    {
        try {
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $monthData = CsOrder::whereIn('shop_id', $shopIds)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->selectRaw('
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as avg_order_amount,
                    COUNT(DISTINCT user_id) as unique_customers
                ')
                ->first();

            return [
                'orders' => $monthData->total_orders ?? 0,
                'revenue' => $monthData->total_revenue ?? 0,
                'avg_order_amount' => $monthData->avg_order_amount ?? 0,
                'customers' => $monthData->unique_customers ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('获取本月汇总数据失败: ' . $e->getMessage());
            return ['orders' => 0, 'revenue' => 0, 'avg_order_amount' => 0, 'customers' => 0];
        }
    }

    /**
     * 获取门店状态汇总
     * @param int $companyId
     * @return array
     */
    private function getShopSummary(int $companyId): array
    {
        try {
            $shopStats = CsShop::where('company_id', $companyId)
                ->selectRaw('
                    COUNT(*) as total_shops,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_shops,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive_shops
                ')
                ->first();

            return [
                'total' => $shopStats->total_shops ?? 0,
                'active' => $shopStats->active_shops ?? 0,
                'inactive' => $shopStats->inactive_shops ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('获取门店状态汇总失败: ' . $e->getMessage());
            return ['total' => 0, 'active' => 0, 'inactive' => 0];
        }
    }

    /**
     * 获取桌台状态汇总
     * @param int $companyId
     * @return array
     */
    private function getTableSummary(int $companyId): array
    {
        try {
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $tableStats = CsTable::whereIn('shop_id', $shopIds)
                ->selectRaw('
                    COUNT(*) as total_tables,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as occupied_tables,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available_tables
                ')
                ->first();

            return [
                'total' => $tableStats->total_tables ?? 0,
                'occupied' => $tableStats->occupied_tables ?? 0,
                'available' => $tableStats->available_tables ?? 0,
                'occupancy_rate' => $tableStats->total_tables > 0 
                    ? round(($tableStats->occupied_tables / $tableStats->total_tables) * 100, 2) 
                    : 0
            ];
        } catch (\Exception $e) {
            Log::error('获取桌台状态汇总失败: ' . $e->getMessage());
            return ['total' => 0, 'occupied' => 0, 'available' => 0, 'occupancy_rate' => 0];
        }
    }

    /**
     * 获取营收趋势数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @return array
     */
    private function getRevenueTrendData(int $companyId, string $period, int $shopId): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $query = CsOrder::whereIn('shop_id', $shopIds);
            
            switch ($period) {
                case 'week':
                    $query->where('created_at', '>=', date('Y-m-d', strtotime('-6 days')));
                    $groupBy = 'DATE(created_at)';
                    break;
                case 'month':
                    $query->where('created_at', '>=', date('Y-m-d', strtotime('-29 days')));
                    $groupBy = 'DATE(created_at)';
                    break;
                case 'quarter':
                    $query->where('created_at', '>=', date('Y-m-d', strtotime('-89 days')));
                    $groupBy = 'DATE(created_at)';
                    break;
                default:
                    $query->where('created_at', '>=', date('Y-m-d', strtotime('-6 days')));
                    $groupBy = 'DATE(created_at)';
            }
            
            $trendData = $query->selectRaw("
                    {$groupBy} as date,
                    COUNT(*) as orders,
                    SUM(total_amount) as revenue
                ")
                ->groupBy(Db::raw($groupBy))
                ->orderBy('date')
                ->get();

            return $trendData->toArray();
        } catch (\Exception $e) {
            Log::error('获取营收趋势数据失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取门店排行数据
     * @param int $companyId
     * @param string $period
     * @param int $limit
     * @return array
     */
    private function getShopRankingData(int $companyId, string $period, int $limit): array
    {
        try {
            $query = CsOrder::join('cs_shops', 'cs_orders.shop_id', '=', 'cs_shops.id')
                ->where('cs_shops.company_id', $companyId);
            
            switch ($period) {
                case 'today':
                    $query->whereDate('cs_orders.created_at', date('Y-m-d'));
                    break;
                case 'week':
                    $query->where('cs_orders.created_at', '>=', date('Y-m-d', strtotime('-6 days')));
                    break;
                case 'month':
                    $query->whereMonth('cs_orders.created_at', date('m'))
                          ->whereYear('cs_orders.created_at', date('Y'));
                    break;
            }
            
            $rankingData = $query->selectRaw('
                    cs_shops.id as shop_id,
                    cs_shops.shop_name,
                    COUNT(cs_orders.id) as total_orders,
                    SUM(cs_orders.total_amount) as total_revenue,
                    AVG(cs_orders.total_amount) as avg_order_amount
                ')
                ->groupBy('cs_shops.id', 'cs_shops.shop_name')
                ->orderBy('total_revenue', 'desc')
                ->limit($limit)
                ->get();

            return $rankingData->toArray();
        } catch (\Exception $e) {
            Log::error('获取门店排行数据失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取商品排行数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @param int $limit
     * @return array
     */
    private function getGoodsRankingData(int $companyId, string $period, int $shopId, int $limit): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $query = CsOrderGoods::join('cs_orders', 'cs_order_goods.order_id', '=', 'cs_orders.id')
                ->join('cs_goods', 'cs_order_goods.goods_id', '=', 'cs_goods.id')
                ->whereIn('cs_orders.shop_id', $shopIds);
            
            switch ($period) {
                case 'today':
                    $query->whereDate('cs_orders.created_at', date('Y-m-d'));
                    break;
                case 'week':
                    $query->where('cs_orders.created_at', '>=', date('Y-m-d', strtotime('-6 days')));
                    break;
                case 'month':
                    $query->whereMonth('cs_orders.created_at', date('m'))
                          ->whereYear('cs_orders.created_at', date('Y'));
                    break;
            }
            
            $rankingData = $query->selectRaw('
                    cs_goods.id as goods_id,
                    cs_goods.goods_name,
                    cs_goods.price,
                    SUM(cs_order_goods.quantity) as total_quantity,
                    SUM(cs_order_goods.quantity * cs_order_goods.price) as total_revenue
                ')
                ->groupBy('cs_goods.id', 'cs_goods.goods_name', 'cs_goods.price')
                ->orderBy('total_quantity', 'desc')
                ->limit($limit)
                ->get();

            return $rankingData->toArray();
        } catch (\Exception $e) {
            Log::error('获取商品排行数据失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取客户分析数据
     * @param int $companyId
     * @param string $period
     * @param int $shopId
     * @return array
     */
    private function getCustomerAnalysisData(int $companyId, string $period, int $shopId): array
    {
        try {
            $shopIds = $shopId > 0 
                ? [$shopId] 
                : CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            
            $query = CsOrder::whereIn('shop_id', $shopIds);
            
            switch ($period) {
                case 'today':
                    $query->whereDate('created_at', date('Y-m-d'));
                    break;
                case 'week':
                    $query->where('created_at', '>=', date('Y-m-d', strtotime('-6 days')));
                    break;
                case 'month':
                    $query->whereMonth('created_at', date('m'))
                          ->whereYear('created_at', date('Y'));
                    break;
            }
            
            // 客户统计
            $customerStats = $query->selectRaw('
                    COUNT(DISTINCT user_id) as unique_customers,
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue
                ')
                ->first();
            
            // 新老客户分析
            $newCustomerQuery = clone $query;
            $newCustomers = $newCustomerQuery->whereHas('csUser', function($q) use ($period) {
                switch ($period) {
                    case 'today':
                        $q->whereDate('created_at', date('Y-m-d'));
                        break;
                    case 'week':
                        $q->where('created_at', '>=', date('Y-m-d', strtotime('-6 days')));
                        break;
                    case 'month':
                        $q->whereMonth('created_at', date('m'))
                          ->whereYear('created_at', date('Y'));
                        break;
                }
            })->count('DISTINCT user_id');

            return [
                'total_customers' => $customerStats->unique_customers ?? 0,
                'total_orders' => $customerStats->total_orders ?? 0,
                'total_revenue' => $customerStats->total_revenue ?? 0,
                'new_customers' => $newCustomers,
                'returning_customers' => ($customerStats->unique_customers ?? 0) - $newCustomers,
                'avg_order_per_customer' => $customerStats->unique_customers > 0 
                    ? round($customerStats->total_orders / $customerStats->unique_customers, 2) 
                    : 0,
                'avg_revenue_per_customer' => $customerStats->unique_customers > 0 
                    ? round($customerStats->total_revenue / $customerStats->unique_customers, 2) 
                    : 0
            ];
        } catch (\Exception $e) {
            Log::error('获取客户分析数据失败: ' . $e->getMessage());
            return [
                'total_customers' => 0,
                'total_orders' => 0,
                'total_revenue' => 0,
                'new_customers' => 0,
                'returning_customers' => 0,
                'avg_order_per_customer' => 0,
                'avg_revenue_per_customer' => 0
            ];
        }
    }
}