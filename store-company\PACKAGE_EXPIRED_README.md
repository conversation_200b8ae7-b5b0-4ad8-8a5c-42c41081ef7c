# 套餐过期提示页面

## 概述

这是一个简洁明了的套餐过期提示页面，参考主流设计风格，采用温暖的米黄色背景和简洁的布局。页面重点突出，用户体验友好。

## 功能特性

### 🎨 设计特色
- **简洁明了**: 采用温暖的米黄色背景 (#FFF8E7, #FFF4D6)
- **重点突出**: 关键信息清晰可见，避免信息过载
- **视觉引导**: 使用喇叭图标和声波动画传达"通知"概念
- **温和色调**: 棕色系文字，营造友好的用户体验

### 📱 响应式设计
- **桌面端优化**: 最佳显示效果，充分利用屏幕空间
- **移动端适配**: 完美适配手机和平板设备
- **弹性布局**: 使用 CSS Grid 和 Flexbox 实现自适应布局

### 🔧 功能模块

#### 1. 视觉提示
- 喇叭图标 (📢) 传达通知概念
- 声波动画效果增强视觉吸引力
- 弹跳动画让图标更生动

#### 2. 核心信息
- **主标题**: "您的试用已到期"
- **说明文字**: 简洁明了的原因和解决方案
- **操作引导**: 明确的下一步操作指示

#### 3. 操作按钮
- **重新登录**: 引导用户回到登录页面
- 温和的棕色系按钮设计
- 悬停效果提升交互体验

#### 4. 友好提示
- 向客服咨询套餐详情的建议
- 重新登录后即可正常使用的承诺
- 积极正面的用户引导

## 技术实现

### 技术栈
- **Vue 3**: 使用 Composition API
- **TypeScript**: 完整类型定义
- **Element Plus**: UI 组件库
- **SCSS**: 样式预处理器

### 核心代码结构

```vue
<template>
  <div class="expired-container">
    <div class="expired-card">
      <!-- 喇叭图标 -->
      <div class="icon-section">
        <div class="megaphone-icon">
          <div class="megaphone">📢</div>
          <div class="sound-waves">...</div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="content-section">
        <h1 class="main-title">您的试用已到期</h1>
        <p class="subtitle">为避免影响正常使用，</p>
        <p class="subtitle">请及时联系客服购买正式套餐</p>

        <button class="renew-btn" @click="contactService">
          重新登录
        </button>

        <p class="footer-text">
          向客服咨询套餐详情以当日起生效，重新登录后即可正常使用
        </p>
      </div>
    </div>
  </div>
</template>
```

### 样式系统

#### 关键样式特点
```css
/* 温暖的背景色 */
.expired-container {
  background: linear-gradient(135deg, #FFF8E7 0%, #FFF4D6 100%);
}

/* 棕色系文字 */
.main-title {
  color: #8B4513;
}

/* 温和的按钮设计 */
.renew-btn {
  background: linear-gradient(135deg, #D2691E, #CD853F);
}
```

#### 关键动画
- **bounce**: 喇叭图标的弹跳动画
- **wave-animation**: 声波的扩散动画
- **slideUp**: 卡片滑入动画

## 使用方法

### 1. 路由配置

页面已添加到路由配置中：

```typescript
{
  path: '/package-expired',
  name: 'PackageExpired',
  component: () => import('@/views/PackageExpired.vue'),
  meta: { requiresAuth: false }
}
```

### 2. 访问页面

- **开发环境**: http://localhost:5174/package-expired
- **生产环境**: https://your-domain.com/package-expired

### 3. 集成到系统

可以在以下场景中使用：

1. **登录检查**: 用户登录时检测套餐状态
2. **定时检查**: 系统定期检查套餐有效期
3. **功能拦截**: 访问受限功能时跳转
4. **主动提醒**: 套餐即将到期时提醒

### 4. 自定义配置

可以通过修改组件中的文字内容来自定义：

```typescript
// 主要文字内容
const title = "您的试用已到期"
const subtitle1 = "为避免影响正常使用，"
const subtitle2 = "请及时联系客服购买正式套餐"
const buttonText = "重新登录"
const footerText = "向客服咨询套餐详情以当日起生效，重新登录后即可正常使用"
```

## 文件结构

```
store-company/
├── src/
│   ├── views/
│   │   └── PackageExpired.vue          # 主页面组件
│   └── router/
│       └── index.ts                    # 路由配置 (已更新)
├── package-expired-demo.html           # 演示页面
└── PACKAGE_EXPIRED_README.md          # 说明文档
```

## 浏览器支持

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 开发说明

### 本地开发

1. 启动开发服务器：
```bash
npm run dev
```

2. 访问页面：
```
http://localhost:5174/package-expired
```

### 自定义样式

所有样式都使用 CSS 变量定义，可以轻松自定义主题色彩：

```css
:root {
  --primary-steel-blue: #your-color;
  --accent-soft-gold: #your-accent;
}
```

### 扩展功能

可以根据需要扩展以下功能：

1. **多语言支持**: 使用 vue-i18n
2. **联系方式**: 添加具体的客服联系方式
3. **倒计时**: 显示试用期剩余时间
4. **套餐介绍**: 链接到套餐详情页面

## 设计理念

这个页面的设计遵循以下原则：

1. **简洁明了**: 避免信息过载，突出重点
2. **用户友好**: 温和的色调和友好的文字表达
3. **视觉引导**: 清晰的操作指引，减少用户困惑
4. **响应式**: 适配各种设备和屏幕尺寸
5. **轻量级**: 简单的实现，快速加载

## 联系方式

如有问题或建议，请联系开发团队。
