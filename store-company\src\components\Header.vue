<template>
  <header class="header">
    <div class="header-left">
      <button class="sidebar-toggle" @click="toggleSidebar">
        <span>☰</span>
      </button>
      <div class="breadcrumb">
        <span>{{ getCurrentPageTitle() }}</span>
      </div>
    </div>

    <div class="header-right">
      <!-- <button class="header-notification" @click="showNotifications">
        <span>🔔</span>
        <div class="notification-badge" v-if="notifications.unread > 0"></div>
      </button> -->

      <div class="user-info" @click="showUserMenu = !showUserMenu">
        <div class="user-avatar">管</div>
        <span class="user-name">{{ currentUser.nickname }}</span>
        <div class="user-dropdown-arrow">▼</div>
      </div>

      <!-- 用户下拉菜单 -->
      <div v-if="showUserMenu" class="user-dropdown" @click.stop>
        <div class="user-menu-item" @click="goToProfile">
          <span class="menu-icon">👤</span>
          个人中心
        </div>
        <div class="user-menu-item" @click="showVersionInfo">
          <span class="menu-icon">📊</span>
          版本信息
        </div>
        <!-- <div class="user-menu-item" @click="changePassword">
          <span class="menu-icon">🔒</span>
          修改密码
        </div> -->
        <div class="user-menu-divider"></div>
        <div class="user-menu-item" @click="logout">
          <span class="menu-icon">🚪</span>
          退出登录
        </div>
      </div>
    </div>
  </header>

  <!-- 个人中心对话框 -->
  <ProfileDialog
    :visible="profileDialogVisible"
    @update:visible="profileDialogVisible = $event"
    @profile-updated="handleProfileUpdated"
  />

  <!-- 版本信息对话框 -->
  <VersionInfoDialog 
    v-model="versionInfoVisible" 
    :user-info="userInfo" 
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import ProfileDialog from './ProfileDialog.vue'
import VersionInfoDialog from './VersionInfoDialog.vue'
import { getUserInfo } from '@/services/authApi'
import type { UserInfo } from '@/types'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const showUserMenu = ref(false)
const profileDialogVisible = ref(false)
const versionInfoVisible = ref(false)
const userInfo = ref<UserInfo | null>(null)
const notifications = ref({
  unread: 3 // 示例数据
})

const currentUser = computed(() => {
  return authStore.user || { nickname: '系统管理员', username: 'admin' }
})

const pageMap = {
  dashboard: '数据大屏',
  shops: '门店管理',
  permissions: '权限管理',
  'permissions-admin': '管理员管理',
  'permissions-role': '角色管理'
}

const getCurrentPageTitle = () => {
  const routeName = route.name as string
  return pageMap[routeName as keyof typeof pageMap] || '企业管理系统'
}

// 获取从MainLayout注入的侧边栏切换方法
const injectedToggleSidebar = inject<() => void>('toggleSidebar')

const toggleSidebar = () => {
  if (injectedToggleSidebar) {
    injectedToggleSidebar()
  } else {
    console.log('Toggle sidebar method not found')
  }
}

const showNotifications = () => {
  // 显示通知面板
  console.log('Show notifications')
}

const goToProfile = () => {
  profileDialogVisible.value = true
  showUserMenu.value = false
}

// 显示版本信息
const showVersionInfo = async () => {
  try {
    if (!userInfo.value) {
      const userData = await getUserInfo()
      userInfo.value = userData
    }
    versionInfoVisible.value = true
    showUserMenu.value = false
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取版本信息失败，请重试')
    showUserMenu.value = false
  }
}

/* const changePassword = () => {
  // 修改密码逻辑
  console.log('Change password')
  showUserMenu.value = false
} */

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
  showUserMenu.value = false
}

// 处理个人资料更新
const handleProfileUpdated = (updatedData: any) => {
  // 更新store中的用户信息
  if (authStore.user) {
    authStore.user.nickname = updatedData.nickname
    authStore.user.username = updatedData.username
  }
  console.log('个人资料已更新:', updatedData)
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.user-info') && !target.closest('.user-dropdown')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  // 如果没有token，为了开发测试设置一个mock token
  if (!authStore.isAuthenticated) {
    localStorage.setItem('token', 'mock_token_dev')
    // 重新设置store中的token
    authStore.token = 'mock_token_dev'
  }
  
  // 初始化用户信息
  authStore.initUserInfo()
  
  // 调试：检查用户信息
  console.log('Current user:', authStore.user)
  console.log('Is authenticated:', authStore.isAuthenticated)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  /* 主要蓝色系 - 深邃商务蓝 */
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  
  /* 辅助蓝色系 - 钢铁蓝与淡雅蓝灰 */
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  
  /* 点缀色系 - 精致金属色 */
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  
  /* 状态色系 */
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  /* 文字颜色系统 */
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  
  /* 背景和效果 */
  --glass-background: rgba(255, 255, 255, 0.92);
  --glass-border: rgba(255, 255, 255, 0.8);
  --shadow-deep: rgba(13, 27, 42, 0.25);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-light: rgba(13, 27, 42, 0.08);
}

.header {
  height: 56px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px var(--shadow-light);
  position: relative;
  z-index: 999;
  /* 保持与 MainLayout 一致的高度和内边距 */
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  transition: all 0.3s ease;
  font-size: 16px;
}

.sidebar-toggle:hover {
  background: var(--secondary-light-blue-gray);
}

.breadcrumb {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.header-notification {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.3s ease;
  font-size: 16px;
}

.header-notification:hover {
  background: var(--secondary-light-blue-gray);
}

.notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: var(--danger-color);
  border-radius: 50%;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.user-info:hover {
  background: var(--secondary-light-blue-gray);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #0D1B2A;
  margin-right: 12px;
  font-size: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.user-name {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  margin-right: 8px;
}

.user-dropdown-arrow {
  font-size: 12px;
  color: var(--text-muted);
  transition: transform 0.3s ease;
}

.user-info:hover .user-dropdown-arrow {
  transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--shadow-medium);
  border: 1px solid rgba(65, 90, 119, 0.1);
  min-width: 160px;
  padding: 8px 0;
  margin-top: 8px;
  z-index: 1000;
}

.user-dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  right: 20px;
  width: 12px;
  height: 12px;
  background: white;
  border: 1px solid rgba(65, 90, 119, 0.1);
  border-right: none;
  border-bottom: none;
  transform: rotate(45deg);
}

.user-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: var(--text-secondary);
}

.user-menu-item:hover {
  background: var(--secondary-light-blue-gray);
  color: var(--text-primary);
}

.menu-icon {
  margin-right: 8px;
  font-size: 16px;
}

.user-menu-divider {
  height: 1px;
  background: rgba(65, 90, 119, 0.1);
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }
  
  .user-name {
    display: none;
  }
  
  .breadcrumb {
    font-size: 14px;
  }
}
</style>