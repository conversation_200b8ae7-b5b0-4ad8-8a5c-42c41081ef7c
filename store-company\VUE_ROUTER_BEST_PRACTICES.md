# Vue 3 路由跳转最佳实践

## 问题说明

在Vue 3项目中，发现了使用 `window.location.href` 进行页面跳转的不规范做法。这种方式会导致以下问题：

1. **破坏SPA体验**：强制刷新整个页面，失去单页应用的优势
2. **状态丢失**：Vue应用状态、Pinia store状态等会被重置
3. **性能问题**：重新加载整个应用，影响用户体验
4. **路由守卫失效**：无法触发Vue Router的路由守卫
5. **历史记录问题**：可能影响浏览器历史记录的正常管理

## 修复内容

### 修复的文件

1. **store-company/src/services/api.ts**
   - 第95行：`window.location.href = '/login'` → `router.push('/login')`
   - 第102行：`window.location.href = '/package-expired'` → `router.push('/package-expired')`

2. **store-shop/src/services/api.ts**
   - 第101行：`window.location.href = '/login'` → `router.push('/login')`

### 添加的导入

```typescript
import router from '@/router'
```

## Vue 3 路由跳转正确方式

### 1. 在组件中使用 useRouter

```vue
<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 编程式导航
const goToLogin = () => {
  router.push('/login')
}

// 带参数的跳转
const goToUserDetail = (userId: number) => {
  router.push(`/user/${userId}`)
  // 或者
  router.push({ name: 'UserDetail', params: { id: userId } })
}

// 带查询参数的跳转
const goToSearch = (keyword: string) => {
  router.push({ path: '/search', query: { q: keyword } })
}

// 替换当前历史记录（不会在历史记录中留下记录）
const replaceToLogin = () => {
  router.replace('/login')
}

// 前进/后退
const goBack = () => {
  router.back()
  // 或者
  router.go(-1)
}

const goForward = () => {
  router.forward()
  // 或者
  router.go(1)
}
</script>
```

### 2. 在非组件文件中使用路由

```typescript
// services/api.ts
import router from '@/router'

// HTTP拦截器中的跳转
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 正确的跳转方式
      router.push('/login')
    }
    return Promise.reject(error)
  }
)
```

### 3. 声明式导航（模板中）

```vue
<template>
  <!-- 基本跳转 -->
  <router-link to="/login">登录</router-link>
  
  <!-- 命名路由 -->
  <router-link :to="{ name: 'Login' }">登录</router-link>
  
  <!-- 带参数 -->
  <router-link :to="{ name: 'UserDetail', params: { id: 123 } }">
    用户详情
  </router-link>
  
  <!-- 带查询参数 -->
  <router-link :to="{ path: '/search', query: { q: 'vue' } }">
    搜索
  </router-link>
  
  <!-- 替换历史记录 -->
  <router-link to="/login" replace>登录</router-link>
  
  <!-- 外部链接（这种情况可以使用普通a标签） -->
  <a href="https://example.com" target="_blank">外部链接</a>
</template>
```

### 4. 路由守卫中的跳转

```typescript
// router/index.ts
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 正确的重定向方式
    next('/login')
    // 或者
    next({ name: 'Login' })
  } else {
    next()
  }
})
```

## 什么时候可以使用 window.location

只有在以下特殊情况下才应该使用 `window.location`：

### 1. 跳转到外部网站

```typescript
// 跳转到外部网站
window.location.href = 'https://www.example.com'

// 或者使用 window.open
window.open('https://www.example.com', '_blank')
```

### 2. 需要完全刷新页面的场景

```typescript
// 强制刷新当前页面
window.location.reload()

// 跳转到同域名下的非Vue应用页面
window.location.href = '/legacy-page.html'
```

### 3. 需要修改URL但不触发路由的场景

```typescript
// 修改URL但不导航（很少使用）
window.history.replaceState(null, '', '/new-url')
```

## 最佳实践总结

1. **优先使用Vue Router**：在Vue应用内部跳转始终使用 `router.push()` 或 `router.replace()`
2. **保持SPA体验**：避免不必要的页面刷新
3. **利用路由守卫**：使用Vue Router的导航守卫进行权限控制
4. **类型安全**：使用TypeScript时，为路由参数定义类型
5. **错误处理**：在路由跳转时添加适当的错误处理

```typescript
// 带错误处理的路由跳转
const navigateToUser = async (userId: number) => {
  try {
    await router.push({ name: 'UserDetail', params: { id: userId } })
  } catch (error) {
    console.error('导航失败:', error)
    ElMessage.error('页面跳转失败')
  }
}
```

## 注意事项

1. **异步跳转**：`router.push()` 返回Promise，可以使用 `await` 等待跳转完成
2. **路由参数验证**：确保传递的参数符合路由定义
3. **权限检查**：在跳转前检查用户权限
4. **用户体验**：避免频繁的路由跳转，影响用户体验

通过遵循这些最佳实践，可以确保Vue 3应用的路由管理更加规范、高效和用户友好。
