import service from './api'
import { getList, getDetail, create, update, remove, toggleStatus } from './baseApi'
import type { PaginationParams, PaginationResponse } from './baseApi'

// 角色接口类型
export interface Role {
  id: number;
  role_name: string;
  description: string;
  permissions: string[];
  role_permission?: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 权限接口类型
export interface Permission {
  id: number;
  permission_name: string;
  permission_key: string;
  parent_id: number;
  level: number;
  sort: number;
  children?: Permission[];
}

// 角色列表参数
export interface RoleListParams extends PaginationParams {
  role_name?: string;
  status?: number;
}

// 角色表单数据
export interface RoleFormData {
  id?: number;
  role_name: string;
  description: string;
  permissions: string[];
  role_permission?: string;
  status: number;
}

const BASE_URL = '/company/CsCompanyAdminRole'

// 获取角色列表
export const getRoleList = (params: RoleListParams = {}): Promise<PaginationResponse<Role>> => {
  return getList<Role>(`${BASE_URL}/index`, params)
}

// 获取角色详情
export const getRoleDetail = (id: number): Promise<Role> => {
  return getDetail<Role>(`${BASE_URL}/edit`, id)
}

// 新增角色
export const addRole = (data: RoleFormData): Promise<any> => {
  return create(`${BASE_URL}/addPost`, data)
}

// 更新角色
export const updateRole = (data: RoleFormData): Promise<any> => {
  return update(`${BASE_URL}/editPost`, data)
}

// 删除角色
export const deleteRole = (id: number): Promise<any> => {
  return service.post(`${BASE_URL}/del`, { id })
}

// 批量删除角色
export const batchDeleteRoles = (ids: number[]): Promise<any> => {
  return service.post(`${BASE_URL}/selectDel`, { ids: ids.join(',') })
}

// 获取权限菜单（用于角色权限设置）
export const getPermissionMenu = (): Promise<any> => {
  return service.get(`${BASE_URL}/add`)
}

// 获取角色选项列表（用于下拉选择）
export const getRoleOptions = (): Promise<{ id: number; role_name: string }[]> => {
  return service.get(`${BASE_URL}/options`, {
    params: {
      status: 1 // 只获取启用的角色
    }
  })
}
