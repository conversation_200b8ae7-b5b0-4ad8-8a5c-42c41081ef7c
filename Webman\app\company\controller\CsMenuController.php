<?php

namespace app\company\controller;

use app\controller\ShopBaseController;
use support\Request;
use support\Response;
use think\exception\ValidateException;

class CsMenuController extends ShopBaseController
{
    // 验证器
    protected $validateName = 'CsMenuValidate';

    // 当前主模型
    protected $modelName = 'CsMenu';

    /**
     * 添加保存
     * @param Request $request
     * @return Response
     */
    public function addPost(Request $request): Response
    {
        try {
            $model = 'app\model\\' . $this->modelName;
            $data = $request->onlyPost($model::$validateFields);
            $data['menu_type'] = 1;
            if (isset($data['id'])) {
                unset($data['id']);
            }
            $validate = new ('app\validate\\' . $this->validateName);
            if (!$validate->check($data)) {
                throw new ValidateException($validate->getError());
            }
            $result = $model::addPost($data);
            if (!empty($result['error'])) {
                throw new \Exception($result['msg']);
            }
            return success('添加成功');
        } catch (\Exception $e) {
            $errMsg = $e->getMessage();
        } catch (ValidateException $e) {
            $errMsg = $e->getError();
        }
        return fail($errMsg);
    }

    protected function getListWhere(): array
    {
        $where = [
            ['menu_type', '=', 1],
        ];
        return $where;
    }

}
