<template>
  <div class="comparison-toggle-wrapper">
    <!-- 对比模式切换按钮 -->
    <div class="comparison-toggle">
      <button 
        class="comparison-toggle-btn"
        :class="{ active: mode === 'yoy' }"
        @click="handleModeChange('yoy')"
      >
        同比对比
      </button>
      <button 
        class="comparison-toggle-btn"
        :class="{ active: mode === 'mom' }"
        @click="handleModeChange('mom')"
      >
        环比对比
      </button>
      
      <!-- 期间选择器 -->
      <div class="period-selector">
        <span class="period-label">基准期:</span>
        <select 
          :value="period" 
          @change="handlePeriodChange"
          class="period-select"
        >
          <option 
            v-for="periodOption in availablePeriods" 
            :key="periodOption.value" 
            :value="periodOption.value"
          >
            {{ periodOption.label }}
          </option>
        </select>
      </div>
    </div>
    
    <!-- 快捷时间范围选择（可选） -->
    <div class="quick-range-selector" v-if="showQuickRanges">
      <button 
        v-for="range in quickRanges"
        :key="range.value"
        class="quick-range-btn"
        :class="{ active: period === range.value }"
        @click="handlePeriodChange({ target: { value: range.value } })"
      >
        {{ range.label }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';

interface PeriodOption {
  value: string;
  label: string;
}

interface Props {
  mode: 'yoy' | 'mom';
  period: string;
  showQuickRanges?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showQuickRanges: false
});

const emit = defineEmits<{
  'update:mode': [mode: 'yoy' | 'mom'];
  'update:period': [period: string];
  'change': [mode: 'yoy' | 'mom', period: string];
}>();

// 可选择的期间
const availablePeriods = computed<PeriodOption[]>(() => {
  if (props.mode === 'yoy') {
    return [
      { value: 'last_year_today', label: '去年同日' },
      { value: 'last_year_week', label: '去年同周' },
      { value: 'last_year_month', label: '去年同月' },
      { value: 'last_year_quarter', label: '去年同季度' },
      { value: 'last_year_year', label: '去年同期' }
    ];
  } else {
    return [
      { value: 'yesterday', label: '昨日' },
      { value: 'last_week', label: '上周' },
      { value: 'last_month', label: '上月' },
      { value: 'last_quarter', label: '上季度' },
      { value: 'last_half_year', label: '上半年' }
    ];
  }
});

// 快捷时间范围
const quickRanges = computed<PeriodOption[]>(() => {
  if (props.mode === 'yoy') {
    return [
      { value: 'last_year_today', label: '去年今日' },
      { value: 'last_year_month', label: '去年本月' }
    ];
  } else {
    return [
      { value: 'yesterday', label: '昨日' },
      { value: 'last_week', label: '上周' },
      { value: 'last_month', label: '上月' }
    ];
  }
});

// 方法
const handleModeChange = (newMode: 'yoy' | 'mom') => {
  emit('update:mode', newMode);
  
  // 切换模式时自动选择对应的默认期间
  const defaultPeriod = newMode === 'yoy' ? 'last_year_today' : 'yesterday';
  emit('update:period', defaultPeriod);
  emit('change', newMode, defaultPeriod);
};

const handlePeriodChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  const newPeriod = target.value;
  
  emit('update:period', newPeriod);
  emit('change', props.mode, newPeriod);
};
</script>

<style scoped>
.comparison-toggle-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.comparison-toggle {
  display: inline-flex;
  align-items: center;
  background: white;
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 8px var(--shadow-light);
  border: 1px solid #e4e7ed;
}

.comparison-toggle-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.comparison-toggle-btn:hover {
  color: var(--primary-business-blue);
  background: rgba(27, 54, 93, 0.05);
}

.comparison-toggle-btn.active {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 2px 4px rgba(27, 54, 93, 0.2);
}

.comparison-toggle-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: var(--accent-soft-gold);
  border-radius: 50%;
}

.period-selector {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
  padding: 6px 12px;
  background: var(--secondary-light-blue-gray);
  border-radius: 6px;
  border: 1px solid var(--accent-platinum);
}

.period-label {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
}

.period-select {
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 13px;
  outline: none;
  cursor: pointer;
  min-width: 80px;
}

.period-select option {
  background: white;
  color: var(--text-primary);
  padding: 8px;
}

.quick-range-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-range-btn {
  padding: 6px 12px;
  border: 1px solid var(--accent-platinum);
  background: white;
  color: var(--text-secondary);
  font-size: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.quick-range-btn:hover {
  border-color: var(--primary-business-blue);
  color: var(--primary-business-blue);
}

.quick-range-btn.active {
  background: var(--primary-business-blue);
  color: white;
  border-color: var(--primary-business-blue);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-toggle {
    flex-direction: column;
    align-items: stretch;
    padding: 8px;
  }
  
  .period-selector {
    margin-left: 0;
    margin-top: 8px;
    justify-content: center;
  }
  
  .quick-range-selector {
    justify-content: center;
  }
}
</style>