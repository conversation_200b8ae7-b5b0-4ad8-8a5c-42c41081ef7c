import service from './api'
import type { LoginResponse } from './api'
import type { UserInfo } from '@/types'

// 登录接口
export const login = async (data: { username: string; password: string }): Promise<LoginResponse> => {
  return service.post('/company/Login/login', data)
}

// 获取用户信息
export const getUserInfo = async (): Promise<UserInfo> => {
  return service.get('/company/Login/getUserInfo')
}

// 退出登录
export const logout = async (): Promise<any> => {
  return service.post('/company/Login/logout')
}

// 修改密码
export const changePassword = async (data: {
  old_password: string;
  new_password: string;
  confirm_password: string;
}): Promise<any> => {
  return service.post('/company/Login/changePassword', data)
}

// 修改个人信息
export const updateProfile = async (data: {
  nickname?: string;
  avatar?: string;
  mobile?: string;
}): Promise<any> => {
  return service.post('/company/Login/updateProfile', data)
}

// 获取客服信息
export const getCustomerServiceInfo = async (): Promise<{
  hotline: string;
  wechat: string;
  email: string;
}> => {
  return service.get('/company/Login/getCustomerServiceInfo')
}

// 忘记密码 - 验证手机号并发送验证码
export const sendForgotPasswordSms = async (data: { phone: string }): Promise<{
  message: string;
  phone: string;
}> => {
  return service.post('/company/Login/sendForgotPasswordSms', data)
}

// 忘记密码 - 验证短信验证码
export const verifyForgotPasswordSms = async (data: {
  phone: string;
  sms_code: string;
}): Promise<{
  message: string;
  token: string; // 用于重置密码的临时token
}> => {
  return service.post('/company/Login/verifyForgotPasswordSms', data)
}

// 忘记密码 - 重置密码
export const resetPassword = async (data: {
  phone: string;
  token: string;
  new_password: string;
  confirm_password: string;
}): Promise<{
  message: string;
}> => {
  return service.post('/company/Login/resetPassword', data)
}

// 忘记密码 - 重新发送验证码
export const resendForgotPasswordSms = async (data: { phone: string }): Promise<{
  message: string;
}> => {
  return service.post('/company/Login/resendForgotPasswordSms', data)
}

// 企业注册 - 发送注册短信验证码
export const sendRegisterSms = async (data: { phone: string }): Promise<{
  message: string;
  phone: string;
  debug_code?: string; // 开发环境下的调试验证码
}> => {
  return service.post('/company/Login/sendRegisterSms', data)
}

// 企业注册 - 注册企业账户
export const registerCompany = async (data: {
  company_name: string;
  phone: string;
  sms_code: string;
  password: string;
  confirm_password: string;
}): Promise<LoginResponse> => {
  return service.post('/company/Login/register', data)
}

// 获取用户协议
export const getUserAgreement = async (): Promise<{
  title: string;
  content: string;
  updated_at: string;
}> => {
  return service.get('/company/Login/getUserAgreement')
}

// 获取隐私政策
export const getPrivacyPolicy = async (): Promise<{
  title: string;
  content: string;
  updated_at: string;
}> => {
  return service.get('/company/Login/getPrivacyPolicy')
}
