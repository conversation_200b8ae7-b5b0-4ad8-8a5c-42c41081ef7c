<?php
/**
 * Dashboard API 调试脚本
 * 用于调试和排查API问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Dashboard API 调试开始 ===\n\n";

// 检查数据库连接
try {
    require_once __DIR__ . '/Webman/vendor/autoload.php';
    
    // 初始化配置
    \Webman\Config::load(config_path(), ['route', 'container']);
    
    echo "✅ Webman 框架初始化成功\n\n";
} catch (Exception $e) {
    echo "❌ Webman 框架初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试数据库连接
try {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=shop_sass_xly;charset=utf8mb4',
        'root',
        'root'
    );
    echo "✅ 数据库连接成功\n\n";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查数据库配置\n\n";
}

// 检查基础数据
$companyId = 1; // 请根据实际情况修改

echo "=== 检查基础数据 ===\n";

// 检查企业数据
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cs_companys WHERE id = ?");
    $stmt->execute([$companyId]);
    $companyCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "企业数据: " . ($companyCount > 0 ? "✅ 存在" : "❌ 不存在") . "\n";
} catch (Exception $e) {
    echo "❌ 检查企业数据失败: " . $e->getMessage() . "\n";
}

// 检查门店数据
try {
    $stmt = $pdo->prepare("SELECT id, shop_name, status FROM cs_shops WHERE company_id = ?");
    $stmt->execute([$companyId]);
    $shops = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "门店数据: " . count($shops) . " 个门店\n";
    foreach ($shops as $shop) {
        echo "  - 门店ID: {$shop['id']}, 名称: {$shop['shop_name']}, 状态: {$shop['status']}\n";
    }
} catch (Exception $e) {
    echo "❌ 检查门店数据失败: " . $e->getMessage() . "\n";
}

// 检查订单数据
try {
    $shopIds = array_column($shops, 'id');
    if (!empty($shopIds)) {
        $shopIdsStr = implode(',', $shopIds);
        $stmt = $pdo->prepare("SELECT COUNT(*) as count, SUM(real_pay_money) as total_amount FROM cs_orders WHERE shop_id IN ($shopIdsStr) AND order_status >= 2 AND DATE(created_at) = CURDATE()");
        $stmt->execute();
        $orderData = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "今日订单: {$orderData['count']} 笔, 总金额: ¥{$orderData['total_amount']}\n";
        
        // 检查支付方式分布
        $stmt = $pdo->prepare("SELECT payment_type, COUNT(*) as count, SUM(real_pay_money) as amount FROM cs_orders WHERE shop_id IN ($shopIdsStr) AND order_status >= 2 AND DATE(created_at) = CURDATE() AND payment_type > 0 GROUP BY payment_type");
        $stmt->execute();
        $paymentStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "支付方式分布:\n";
        foreach ($paymentStats as $stat) {
            echo "  - 支付方式ID: {$stat['payment_type']}, 笔数: {$stat['count']}, 金额: ¥{$stat['amount']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 检查订单数据失败: " . $e->getMessage() . "\n";
}

// 检查支付方式配置
try {
    if (!empty($shopIds)) {
        $shopIdsStr = implode(',', $shopIds);
        $stmt = $pdo->prepare("SELECT id, shop_id, payment_method_name, status FROM cs_payment_methods WHERE shop_id IN ($shopIdsStr)");
        $stmt->execute();
        $paymentMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "支付方式配置: " . count($paymentMethods) . " 个\n";
        foreach ($paymentMethods as $method) {
            echo "  - ID: {$method['id']}, 门店: {$method['shop_id']}, 名称: {$method['payment_method_name']}, 状态: {$method['status']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 检查支付方式配置失败: " . $e->getMessage() . "\n";
}

// 检查商品订单数据
try {
    if (!empty($shopIds)) {
        $shopIdsStr = implode(',', $shopIds);
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cs_order_goodss WHERE shop_id IN ($shopIdsStr)");
        $stmt->execute();
        $orderGoodsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "订单商品记录: {$orderGoodsCount} 条\n";
        
        // 检查字段是否存在
        $stmt = $pdo->prepare("DESCRIBE cs_order_goodss");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "cs_order_goodss 表字段: " . implode(', ', $columns) . "\n";
    }
} catch (Exception $e) {
    echo "❌ 检查商品订单数据失败: " . $e->getMessage() . "\n";
}

echo "\n=== 调试完成 ===\n";
echo "如果发现数据不足，请执行: mysql < Webman/database/test_data/insert_dashboard_test_data.sql\n";
