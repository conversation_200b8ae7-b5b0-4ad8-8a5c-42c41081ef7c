# 同比/环比数据对比组件库

## 概述

这是一套专为授权企业管理系统设计的同比/环比数据对比组件库，提供完整的数据对比展示解决方案，包括切换控件、增强统计卡片和图表分析面板。

## 功能特性

- 📊 **智能对比切换** - 支持同比/环比模式切换，自动适配期间选择
- 📈 **增强统计卡片** - 丰富的对比信息展示，支持多维度数据对比
- 🎯 **图表分析面板** - 可视化趋势分析，支持详细数据洞察
- 🎨 **视觉设计一致** - 完美融入现有系统设计风格
- 📱 **响应式设计** - 适配各种屏幕尺寸
- ⚡ **高性能** - 优化的动画和渲染性能
- 🔧 **高度可配置** - 支持自定义配置和主题

## 组件结构

```
comparison/
├── ComparisonToggle.vue      # 同比/环比切换组件
├── EnhancedStatCard.vue      # 增强版统计卡片
├── ChartComparisonPanel.vue  # 图表对比分析面板
├── index.ts                  # 组件库入口文件
└── README.md                 # 使用文档
```

## 快速开始

### 1. 引入组件

```typescript
// 完整引入
import { ComparisonSystem } from './comparison';

// 按需引入
import { 
  ComparisonToggle, 
  EnhancedStatCard, 
  ChartComparisonPanel 
} from './comparison';
```

### 2. 基础使用

```vue
<template>
  <ComparisonSystem
    :initial-mode="'yoy'"
    :auto-refresh="true"
    :refresh-interval="30000"
    @comparison-change="handleComparisonChange"
    @data-update="handleDataUpdate"
  />
</template>

<script setup>
import { ComparisonSystem } from './comparison';

const handleComparisonChange = (mode, period) => {
  console.log('对比模式切换:', mode, period);
};

const handleDataUpdate = (data) => {
  console.log('数据更新:', data);
};
</script>
```

## 组件详细说明

### ComparisonToggle - 对比模式切换

**Props:**
- `mode: 'yoy' | 'mom'` - 当前对比模式
- `period: string` - 当前选择的期间
- `showQuickRanges?: boolean` - 是否显示快捷时间范围

**Events:**
- `update:mode` - 模式变更事件
- `update:period` - 期间变更事件
- `change` - 对比参数变更事件

**使用示例:**
```vue
<ComparisonToggle
  v-model:mode="comparisonMode"
  v-model:period="selectedPeriod"
  :show-quick-ranges="true"
  @change="handleComparisonChange"
/>
```

### EnhancedStatCard - 增强统计卡片

**Props:**
- `data: StatData` - 统计数据对象
- `comparisonMode: 'yoy' | 'mom'` - 对比模式
- `basePeriod: string` - 基准期间描述
- `loading?: boolean` - 加载状态

**数据结构:**
```typescript
interface StatData {
  id: string;
  title: string;
  value: number | string;
  icon: string;
  iconColor: 'blue' | 'green' | 'orange' | 'purple';
  comparison?: ComparisonData;
  secondaryMetric?: {
    label: string;
    value: string;
    comparison?: ComparisonData;
  };
}

interface ComparisonData {
  percent: number;      // 百分比变化
  absolute: number;     // 绝对值变化
  trend: 'up' | 'down' | 'equal';  // 趋势方向
  consecutivePeriods?: number;     // 连续期间数
  additionalInfo?: string;         // 额外信息
}
```

**使用示例:**
```vue
<EnhancedStatCard
  :data="{
    id: 'revenue',
    title: '今日营业额',
    value: 125800,
    icon: '💰',
    iconColor: 'green',
    comparison: {
      percent: 15.2,
      absolute: 16600,
      trend: 'up',
      consecutivePeriods: 7
    }
  }"
  :comparison-mode="comparisonMode"
  :base-period="basePeriodText"
  :loading="loading"
  @hover="handleStatHover"
  @click="handleStatClick"
/>
```

### ChartComparisonPanel - 图表对比分析面板

**Props:**
- `comparisonMode: 'yoy' | 'mom'` - 对比模式
- `metrics: ComparisonMetric[]` - 对比指标数组
- `loading?: boolean` - 加载状态

**Events:**
- `mode-change` - 模式变更事件
- `export` - 数据导出事件
- `metric-click` - 指标点击事件

**使用示例:**
```vue
<ChartComparisonPanel
  :comparison-mode="comparisonMode"
  :metrics="comparisonMetrics"
  :loading="loading"
  @mode-change="handleModeChange"
  @export="handleExport"
  @metric-click="handleMetricClick"
/>
```

## 工具函数

组件库提供了丰富的工具函数，可通过 `comparisonUtils` 导入：

```typescript
import { comparisonUtils } from './comparison';

// 格式化百分比
const percentage = comparisonUtils.formatPercentage(15.2); // "+15.2"

// 获取趋势类名
const className = comparisonUtils.getTrendClass('up'); // "positive"

// 格式化数字
const formattedNumber = comparisonUtils.formatNumber(125800); // "12.6万"

// 生成期间选项
const periods = comparisonUtils.generatePeriodOptions('yoy');

// 导出CSV
const csvContent = comparisonUtils.exportToCSV(metrics, 'yoy');
comparisonUtils.downloadCSV(csvContent, 'comparison-data.csv');
```

## 样式定制

组件使用了系统的CSS变量，可以通过修改变量来定制样式：

```css
:root {
  --primary-business-blue: #1B365D;
  --success-color: #67C23A;
  --danger-color: #F56C6C;
  --info-color: #909399;
  /* 更多变量... */
}
```

## 响应式设计

组件自动适配不同屏幕尺寸：

- **桌面端 (≥1200px)**: 完整功能展示
- **平板端 (768px-1024px)**: 自适应布局调整
- **移动端 (<768px)**: 堆叠式布局

## 性能优化

- **按需加载**: 支持组件级按需引入
- **虚拟滚动**: 大数据量时的性能优化
- **防抖节流**: 优化频繁操作的性能
- **缓存策略**: 智能的数据缓存机制

## 最佳实践

### 1. 数据更新频率控制

```typescript
// 推荐使用防抖来控制数据更新频率
import { debounce } from 'lodash-es';

const debouncedUpdate = debounce(fetchComparisonData, 300);
```

### 2. 错误处理

```typescript
const fetchComparisonData = async () => {
  try {
    loading.value = true;
    const data = await api.getComparisonData();
    updateData(data);
  } catch (error) {
    console.error('数据获取失败:', error);
    // 显示错误提示
  } finally {
    loading.value = false;
  }
};
```

### 3. 内存优化

```typescript
// 组件销毁时清理定时器和事件监听
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  // 清理图表实例
  Object.values(chartInstances).forEach(chart => {
    chart.dispose();
  });
});
```

## 浏览器兼容性

- Chrome ≥ 60
- Firefox ≥ 55
- Safari ≥ 12
- Edge ≥ 79

## 更新日志

### v1.0.0
- 🎉 初始版本发布
- ✨ 支持同比/环比对比切换
- ✨ 增强版统计卡片
- ✨ 图表对比分析面板
- ✨ 完整的TypeScript类型支持
- ✨ 响应式设计支持

## 技术支持

如有问题或建议，请联系开发团队或提出Issue。