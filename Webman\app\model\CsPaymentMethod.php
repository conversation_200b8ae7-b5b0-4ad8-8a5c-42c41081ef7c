<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * cs_payment_methods 收款方式表
 * @property integer $id (主键)
 * @property integer $shop_id 所属门店
 * @property string $payment_method_name 收款方式名称
 * @property integer $sort 排序
 * @property integer $status 状态 0:下架 1：上架
 * @property integer $is_default 是否默认 0:否 1：是
 * @property string $remark 备注
 * @property string $created_at 添加时间
 * @property string $updated_at 修改时间
 * @property string $deleted_at 删除时间
 */
class CsPaymentMethod extends ShopBaseModel
{
    use SoftDeletes;

    /**
     * 验证字段
     * @var string[]
     */
    public static array $validateFields = [
        'id', 'shop_id', 'payment_method_name',
        'sort', 'status', 'is_default', 'remark', ];

    public static array $searchFields = [
        'payment_method_name' => 'LIKE',
        'status' => '=',
    ];

    /**
     * 修改保存
     * @param array $data
     * @return array
     */
    public static function editPost(array $data = []): array
    {
        try {
            $model = static::find($data['id']);
            if (empty($model)) {
                return ['error' => 1, 'msg' => '数据已经被删除'];
            }
            foreach ($data as $k => $v) {
                if ($k != 'id') {
                    $model->$k = $v;
                }
            }
            if (!empty($data['is_default'])) {
                // 如果设置为默认，则将其他默认值设置为0
                static::where('shop_id', $data['shop_id'])->where('id', '!=', $data['id'])->update(['is_default' => 0]);
            }
            if ($model->save()) {
                return ['error' => 0, 'msg' => '修改成功'];
            } else {
                return ['error' => 1, 'msg' => '修改失败'];
            }
        } catch (\Exception $e) {
            return ['error' => 1, 'msg' => $e->getMessage()];
        }
    }
}
