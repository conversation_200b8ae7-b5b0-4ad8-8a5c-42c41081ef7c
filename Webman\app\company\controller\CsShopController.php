<?php

namespace app\company\controller;

use app\controller\CompanyBaseController;
use app\model\CsShop;
use app\model\CsShopAdmin;
use app\model\CsOrder;
use app\model\CsTable;
use app\service\ReportService;
use support\Request;
use support\Response;
use support\Log;

class CsShopController extends CompanyBaseController
{
    // 验证器
    protected $validateName = 'CsShopValidate';

    // 当前主模型
    protected $modelName = 'CsShop';

    /**
     * 门店列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 15);
            $shop_name = $request->get('shop_name', '');
            $nickname = $request->get('nickname', '');
            $mobile = $request->get('mobile', '');

            $query = CsShop::where('company_id', $request->company_id);

            // 搜索条件
            if (!empty($shop_name)) {
                $query->where('shop_name', 'like', "%{$shop_name}%");
            }
            if (!empty($nickname)) {
                $query->where('nickname', 'like', "%{$nickname}%");
            }
            if (!empty($mobile)) {
                $query->where('mobile', 'like', "%{$mobile}%");
            }

            $total = $query->count();
            $shops = $query->orderBy('id', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            // 处理营业时间和当天营业状态
            $currentDay = strtolower(date('l')); // 获取当天英文星期名（如monday）
            $currentTime = date('H:i'); // 获取当前时间
            
            foreach ($shops as $shop) {
                $businessHours = !empty($shop->business_hours) ? json_decode($shop->business_hours, true) : [];
                
                // 获取当天营业信息
                $todayInfo = $businessHours[$currentDay] ?? ['is_open' => false, 'open' => '09:00', 'close' => '22:00'];
                
                // 营业状态判断优先级：
                // 1. 门店全局状态为0（关闭），则显示"休息中"
                // 2. 门店全局状态为1（开启），检查当天设置和时间
                if ($shop->status == 0) {
                    // 门店全局关闭
                    $shop->today_status = 0;
                    $shop->today_hours = '休息中';
                    $shop->status_text = '休息中';
                } else {
                    // 门店全局开启，检查当天营业设置
                    if (!$todayInfo['is_open']) {
                        // 当天设置为不营业
                        $shop->today_status = 0;
                        $shop->today_hours = '今日休业';
                        $shop->status_text = '今日休业';
                    } else {
                        // 当天设置为营业，检查当前时间是否在营业时间内
                        $openTime = $todayInfo['open'] ?? '09:00';
                        $closeTime = $todayInfo['close'] ?? '22:00';
                        
                        // 处理跨天营业的情况（比如23:00-02:00）
                        if ($closeTime < $openTime) {
                            // 跨天营业：如果当前时间在开始时间之后或结束时间之前
                            $isOpen = ($currentTime >= $openTime) || ($currentTime <= $closeTime);
                        } else {
                            // 正常营业：当前时间在开始和结束时间之间
                            $isOpen = ($currentTime >= $openTime) && ($currentTime <= $closeTime);
                        }
                        
                        if ($isOpen) {
                            $shop->today_status = 1;
                            $shop->today_hours = $openTime . '-' . $closeTime;
                            $shop->status_text = '营业中';
                        } else {
                            $shop->today_status = 0;
                            $shop->today_hours = $openTime . '-' . $closeTime;
                            // 判断是未开始营业还是已打烊
                            if ($currentTime < $openTime) {
                                $shop->status_text = '未开始营业';
                            } else {
                                $shop->status_text = '已打烊';
                            }
                        }
                    }
                }
                
                // 格式化完整营业时间
                $shop->business_hours_formatted = $this->formatBusinessHours($businessHours);
            }

            return success([
                'list' => $shops,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店列表失败: ' . $e->getMessage());
            return fail('获取门店列表失败');
        }
    }

    /**
     * 新增门店
     * @param Request $request
     * @return Response
     */
    public function addPost(Request $request): Response
    {
        try {
            $data = $request->post();
            $data['company_id'] = $request->company_id;

            // 处理营业时间
            if (isset($data['business_hours']) && is_array($data['business_hours'])) {
                $data['business_hours'] = json_encode($data['business_hours']);
            }

            $result = CsShop::addPost($data);
            
            if ($result['error'] == 0) {
                return success($result['msg']);
            } else {
                return fail($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('新增门店失败: ' . $e->getMessage());
            return fail('新增门店失败');
        }
    }

    /**
     * 获取门店详情（编辑时使用）
     * @param Request $request
     * @return Response
     */
    public function edit(Request $request): Response
    {
        try {
            $id = $request->get('id', 0);
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 解析营业时间
            $businessHours = json_decode($shop->business_hours, true) ?? [];
            $shop->business_hours_config = $businessHours;

            return success($shop);
        } catch (\Exception $e) {
            Log::error('获取门店详情失败: ' . $e->getMessage());
            return fail('获取门店详情失败');
        }
    }

    /**
     * 更新门店
     * @param Request $request
     * @return Response
     */
    public function editPost(Request $request): Response
    {
        try {
            $data = $request->post();
            
            if (empty($data['id'])) {
                return fail('参数错误');
            }

            // 验证门店是否属于当前公司
            $shop = CsShop::where('company_id', $request->company_id)->find($data['id']);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 处理营业时间
            if (isset($data['business_hours']) && is_array($data['business_hours'])) {
                $data['business_hours'] = json_encode($data['business_hours']);
            }

            $result = CsShop::editPost($data);
            
            if ($result['error'] == 0) {
                return success($result['msg']);
            } else {
                return fail($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('更新门店失败: ' . $e->getMessage());
            return fail('更新门店失败');
        }
    }

    /**
     * 删除门店
     * @param Request $request
     * @return Response
     */
    public function delete(Request $request): Response
    {
        try {
            $id = $request->post('id', 0);
            if (empty($id)) {
                return fail('参数错误');
            }

            // 验证门店是否属于当前公司
            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            $result = CsShop::del($id);
            
            if ($result['error'] == 0) {
                return success($result['msg']);
            } else {
                return fail($result['msg']);
            }
        } catch (\Exception $e) {
            Log::error('删除门店失败: ' . $e->getMessage());
            return fail('删除门店失败');
        }
    }

    /**
     * 切换门店当天营业状态
     * @param Request $request
     * @return Response
     */
    public function toggleTodayStatus(Request $request): Response
    {
        try {
            $id = $request->post('id', 0);
            $is_open = $request->post('is_open', 1);
            
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 获取当天
            $currentDay = strtolower(date('l')); // 获取当天英文星期名
            $businessHours = json_decode($shop->business_hours, true) ?? [];
            
            // 更新当天营业状态
            if (!isset($businessHours[$currentDay])) {
                $businessHours[$currentDay] = ['open' => '09:00', 'close' => '22:00'];
            }
            $businessHours[$currentDay]['is_open'] = (bool)$is_open;

            $shop->business_hours = json_encode($businessHours);
            $shop->save();

            Log::info("门店当天营业状态更新成功，门店ID: {$id}, 当天: {$currentDay}, 状态: {$is_open}");
            return success('营业状态更新成功');
        } catch (\Exception $e) {
            Log::error('切换门店营业状态失败: ' . $e->getMessage());
            return fail('切换营业状态失败');
        }
    }

    /**
     * 格式化营业时间显示
     * @param array $businessHours
     * @return string
     */
    private function formatBusinessHours(array $businessHours): string
    {
        $dayNames = [
            'monday' => '周一',
            'tuesday' => '周二', 
            'wednesday' => '周三',
            'thursday' => '周四',
            'friday' => '周五',
            'saturday' => '周六',
            'sunday' => '周日'
        ];

        $formatted = [];
        foreach ($dayNames as $key => $name) {
            $dayInfo = $businessHours[$key] ?? ['is_open' => false];
            if ($dayInfo['is_open']) {
                $formatted[] = $name . ': ' . ($dayInfo['open'] ?? '09:00') . '-' . ($dayInfo['close'] ?? '22:00');
            } else {
                $formatted[] = $name . ': 休息';
            }
        }

        return implode(', ', $formatted);
    }

    /**
     * 获取门店详情（扩展版本，包含更多统计信息）
     * @param Request $request
     * @return Response
     */
    public function detail(Request $request): Response
    {
        try {
            $id = $request->get('id', 0);
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::with(['csCompany'])
                ->where('company_id', $request->company_id)
                ->find($id);

            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 获取门店统计数据
            $stats = $this->getShopStatistics($id);
            $shop->statistics = $stats;

            // 获取管理员信息
            $adminCount = CsShopAdmin::where('shop_id', $id)->count();
            $shop->admin_count = $adminCount;

            // 获取桌台信息
            $tableStats = CsTable::where('shop_id', $id)
                ->selectRaw('COUNT(*) as total_tables, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as occupied_tables')
                ->first();
            $shop->table_stats = $tableStats;

            return success($shop);
        } catch (\Exception $e) {
            Log::error('获取门店详情失败: ' . $e->getMessage());
            return fail('获取门店详情失败');
        }
    }

    /**
     * 获取门店实时状态
     * @param Request $request
     * @return Response
     */
    public function status(Request $request): Response
    {
        try {
            $id = $request->get('id', 0);
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 获取实时数据
            $realTimeData = $this->getRealTimeShopData($id);
            
            return success([
                'shop_info' => [
                    'id' => $shop->id,
                    'shop_name' => $shop->shop_name,
                    'status' => $shop->status,
                    'address' => $shop->address
                ],
                'real_time_data' => $realTimeData
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店状态失败: ' . $e->getMessage());
            return fail('获取门店状态失败');
        }
    }

    /**
     * 获取所有门店状态汇总
     * @param Request $request
     * @return Response
     */
    public function statusSummary(Request $request): Response
    {
        try {
            $shops = CsShop::where('company_id', $request->company_id)
                ->select(['id', 'shop_name', 'status', 'address'])
                ->get();

            $summary = [];
            foreach ($shops as $shop) {
                $realTimeData = $this->getRealTimeShopData($shop->id);
                $summary[] = [
                    'shop_info' => $shop,
                    'real_time_data' => $realTimeData
                ];
            }

            // 计算汇总统计
            $totalShops = count($shops);
            $activeShops = $shops->where('status', 1)->count();
            $inactiveShops = $totalShops - $activeShops;

            return success([
                'shops' => $summary,
                'summary_stats' => [
                    'total_shops' => $totalShops,
                    'active_shops' => $activeShops,
                    'inactive_shops' => $inactiveShops
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店状态汇总失败: ' . $e->getMessage());
            return fail('获取门店状态汇总失败');
        }
    }

    /**
     * 更新门店营业状态
     * @param Request $request
     * @return Response
     */
    public function updateStatus(Request $request): Response
    {
        try {
            $id = $request->post('id', 0);
            $status = $request->post('status', 1);
            
            if (empty($id)) {
                return fail('参数错误');
            }

            $shop = CsShop::where('company_id', $request->company_id)->find($id);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            $shop->status = $status;
            $shop->save();

            Log::info("门店状态更新成功，门店ID: {$id}, 新状态: {$status}");
            return success('门店状态更新成功');
        } catch (\Exception $e) {
            Log::error('更新门店状态失败: ' . $e->getMessage());
            return fail('更新门店状态失败');
        }
    }

    /**
     * 为门店创建管理员账号
     * @param Request $request
     * @return Response
     */
    public function createAdmin(Request $request): Response
    {
        try {
            $shopId = $request->post('shop_id', 0);
            $username = $request->post('username', '');
            $password = $request->post('password', '');
            $mobile = $request->post('mobile', '');
            $nickname = $request->post('nickname', '');

            if (empty($shopId) || empty($username) || empty($password)) {
                return fail('参数错误');
            }

            // 验证门店是否存在且属于当前公司
            $shop = CsShop::where('company_id', $request->company_id)->find($shopId);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            // 检查用户名是否已存在
            $existingAdmin = CsShopAdmin::where('username', $username)->first();
            if ($existingAdmin) {
                return fail('用户名已存在');
            }

            // 创建管理员账号
            $admin = new CsShopAdmin();
            $admin->shop_id = $shopId;
            $admin->username = $username;
            $admin->password = password_hash($password, PASSWORD_DEFAULT);
            $admin->mobile = $mobile;
            $admin->nickname = $nickname;
            $admin->status = 1;
            $admin->save();

            Log::info("门店管理员创建成功，门店ID: {$shopId}, 用户名: {$username}");
            return success('管理员账号创建成功');
        } catch (\Exception $e) {
            Log::error('创建门店管理员失败: ' . $e->getMessage());
            return fail('创建管理员账号失败');
        }
    }

    /**
     * 重置门店管理员密码
     * @param Request $request
     * @return Response
     */
    public function resetPassword(Request $request): Response
    {
        try {
            $adminId = $request->post('admin_id', 0);
            $newPassword = $request->post('new_password', '');

            if (empty($adminId) || empty($newPassword)) {
                return fail('参数错误');
            }

            // 验证管理员是否存在且属于当前公司的门店
            $admin = CsShopAdmin::whereHas('csShop', function($query) use ($request) {
                $query->where('company_id', $request->company_id);
            })->find($adminId);

            if (empty($admin)) {
                return fail('管理员不存在');
            }

            $admin->password = password_hash($newPassword, PASSWORD_DEFAULT);
            $admin->save();

            Log::info("门店管理员密码重置成功，管理员ID: {$adminId}");
            return success('密码重置成功');
        } catch (\Exception $e) {
            Log::error('重置管理员密码失败: ' . $e->getMessage());
            return fail('密码重置失败');
        }
    }

    /**
     * 获取门店管理员列表
     * @param Request $request
     * @return Response
     */
    public function adminList(Request $request): Response
    {
        try {
            $shopId = $request->get('shop_id', 0);
            
            if (empty($shopId)) {
                return fail('参数错误');
            }

            // 验证门店是否存在且属于当前公司
            $shop = CsShop::where('company_id', $request->company_id)->find($shopId);
            if (empty($shop)) {
                return fail('门店不存在');
            }

            $admins = CsShopAdmin::where('shop_id', $shopId)
                ->select(['id', 'username', 'nickname', 'mobile', 'status', 'created_at', 'last_login_at'])
                ->get();

            return success($admins);
        } catch (\Exception $e) {
            Log::error('获取门店管理员列表失败: ' . $e->getMessage());
            return fail('获取管理员列表失败');
        }
    }

    /**
     * 获取门店统计数据
     * @param int $shopId
     * @return array
     */
    private function getShopStatistics(int $shopId): array
    {
        try {
            // 今日订单统计
            $todayOrders = CsOrder::where('shop_id', $shopId)
                ->whereDate('created_at', date('Y-m-d'))
                ->selectRaw('COUNT(*) as total_orders, SUM(total_amount) as total_revenue')
                ->first();

            // 本月订单统计
            $monthOrders = CsOrder::where('shop_id', $shopId)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->selectRaw('COUNT(*) as total_orders, SUM(total_amount) as total_revenue')
                ->first();

            return [
                'today' => [
                    'orders' => $todayOrders->total_orders ?? 0,
                    'revenue' => $todayOrders->total_revenue ?? 0
                ],
                'month' => [
                    'orders' => $monthOrders->total_orders ?? 0,
                    'revenue' => $monthOrders->total_revenue ?? 0
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取门店统计数据失败: ' . $e->getMessage());
            return [
                'today' => ['orders' => 0, 'revenue' => 0],
                'month' => ['orders' => 0, 'revenue' => 0]
            ];
        }
    }

    /**
     * 获取门店实时数据
     * @param int $shopId
     * @return array
     */
    private function getRealTimeShopData(int $shopId): array
    {
        try {
            // 实时桌台状态
            $tableStats = CsTable::where('shop_id', $shopId)
                ->selectRaw('
                    COUNT(*) as total_tables,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as occupied_tables,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available_tables
                ')
                ->first();

            // 今日实时数据
            $todayStats = CsOrder::where('shop_id', $shopId)
                ->whereDate('created_at', date('Y-m-d'))
                ->selectRaw('
                    COUNT(*) as today_orders,
                    SUM(total_amount) as today_revenue,
                    COUNT(DISTINCT user_id) as today_customers
                ')
                ->first();

            return [
                'table_stats' => [
                    'total' => $tableStats->total_tables ?? 0,
                    'occupied' => $tableStats->occupied_tables ?? 0,
                    'available' => $tableStats->available_tables ?? 0
                ],
                'today_stats' => [
                    'orders' => $todayStats->today_orders ?? 0,
                    'revenue' => $todayStats->today_revenue ?? 0,
                    'customers' => $todayStats->today_customers ?? 0
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取门店实时数据失败: ' . $e->getMessage());
            return [
                'table_stats' => ['total' => 0, 'occupied' => 0, 'available' => 0],
                'today_stats' => ['orders' => 0, 'revenue' => 0, 'customers' => 0]
            ];
        }
    }
}
