import service from './api'
import { getList, getDetail, create, update, remove, toggleStatus } from './baseApi'
import type { PaginationParams, PaginationResponse } from './baseApi'

// 营业时间配置
export interface BusinessHours {
  monday: { is_open: boolean; open: string; close: string };
  tuesday: { is_open: boolean; open: string; close: string };
  wednesday: { is_open: boolean; open: string; close: string };
  thursday: { is_open: boolean; open: string; close: string };
  friday: { is_open: boolean; open: string; close: string };
  saturday: { is_open: boolean; open: string; close: string };
  sunday: { is_open: boolean; open: string; close: string };
}

// 门店接口类型
export interface Shop {
  id: number;
  shop_name: string;
  nickname: string;
  mobile: string;
  password?: string;
  address: string;
  business_hours: string | BusinessHours;
  business_hours_config?: BusinessHours;
  business_hours_formatted?: string;
  service_phone?: string;
  latitude?: number;
  longitude?: number;
  status: number;
  today_status?: number;
  today_hours?: string;
  status_text?: string; // 新增：详细状态文本（营业中、已打烊、未开始营业、休息中等）
  remark?: string;
  company_id: number;
  created_at: string;
  updated_at: string;
}

// 门店列表参数
export interface ShopListParams extends PaginationParams {
  shop_name?: string;
  nickname?: string;
  mobile?: string;
}

// 门店表单数据
export interface ShopFormData {
  id?: number;
  shop_name: string;
  nickname: string;
  mobile: string;
  password?: string;
  address: string;
  business_hours?: BusinessHours;
  service_phone?: string;
  latitude?: number;
  longitude?: number;
  status: number;
  remark?: string;
}

const BASE_URL = '/company/CsShop'

// 获取门店列表
export const getShopList = (params: ShopListParams = {}): Promise<PaginationResponse<Shop>> => {
  return service.get(`${BASE_URL}/index`, { params })
}

// 获取门店详情
export const getShopDetail = (id: number): Promise<Shop> => {
  return service.get(`${BASE_URL}/edit`, { params: { id } })
}

// 新增门店
export const addShop = (data: ShopFormData): Promise<any> => {
  return service.post(`${BASE_URL}/addPost`, data)
}

// 更新门店
export const updateShop = (data: ShopFormData): Promise<any> => {
  return service.post(`${BASE_URL}/editPost`, data)
}

// 删除门店
export const deleteShop = (id: number): Promise<any> => {
  return service.post(`${BASE_URL}/delete`, { id })
}

// 切换门店全局状态
export const toggleShopStatus = (id: number, status: number): Promise<any> => {
  return service.post(`${BASE_URL}/updateStatus`, { id, status })
}

// 切换门店当天营业状态
export const toggleTodayStatus = (id: number, is_open: number): Promise<any> => {
  return service.post(`${BASE_URL}/toggleTodayStatus`, { id, is_open })
}

// 获取门店详情（扩展版本，包含统计信息）
export const getShopDetailExtended = (id: number): Promise<Shop> => {
  return service.get(`${BASE_URL}/detail`, { params: { id } })
}

// 获取门店实时状态
export const getShopStatus = (id: number): Promise<any> => {
  return service.get(`${BASE_URL}/status`, { params: { id } })
}

// 获取所有门店状态汇总
export const getShopStatusSummary = (): Promise<any> => {
  return service.get(`${BASE_URL}/statusSummary`)
}
