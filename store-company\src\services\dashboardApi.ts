import service from './api'

// 仪表板数据接口类型定义
export interface DashboardOverview {
  totalShops: number
  onlineShops: number
  newShopsThisMonth: number
  todayRevenue: number
  todayOrders: number
  successOrders: number
  activeMembers: number
}

export interface RevenueTrendData {
  period: string
  xAxis: string[]
  data: number[]
}

export interface ShopComparisonData {
  period: string
  shopNames: string[]
  revenues: number[]
}

export interface PaymentAnalysisData {
  period: string
  total_amount: number
  payment_methods: Array<{
    id: number
    name: string
    amount: number
    percentage: number
    count: number
  }>
}

export interface ProductRankingData {
  period: string
  products: Array<{
    name: string
    sales: number
    revenue: number
  }>
}

export interface ShopStatusData {
  shopList: Array<{
    id: number
    name: string
    status: 'online' | 'offline'
    todayRevenue: number
    todayOrders: number
    avgPrice: number
    profit: number
    stockStatus: 'normal' | 'warning' | 'low'
    lastUpdate: string
  }>
}

/**
 * 获取仪表板概览数据
 */
export async function getDashboardOverview(): Promise<DashboardOverview> {
  return service.get('/company/Dashboard/overview')
}

/**
 * 获取营业额趋势数据
 * @param period 时间周期：today, week, month, year
 */
export async function getRevenueTrend(period: string = 'today'): Promise<RevenueTrendData> {
  return service.get('/company/Dashboard/revenueTrend', {
    params: { period }
  })
}

/**
 * 获取门店业绩对比数据
 * @param period 时间周期：today, week, month, year
 */
export async function getShopComparison(period: string = 'today'): Promise<ShopComparisonData> {
  return service.get('/company/Dashboard/shopComparison', {
    params: { period }
  })
}

/**
 * 获取支付方式分析数据
 * @param period 时间周期：today, week, month, year
 */
export async function getPaymentAnalysis(period: string = 'today'): Promise<PaymentAnalysisData> {
  return service.get('/company/Dashboard/paymentAnalysis', {
    params: { period }
  })
}

/**
 * 获取热销商品排行数据
 * @param period 时间周期：today, week, month, year
 */
export async function getProductRanking(period: string = 'today'): Promise<ProductRankingData> {
  return service.get('/company/Dashboard/productRanking', {
    params: { period }
  })
}

/**
 * 获取门店实时状态数据
 */
export async function getShopStatus(): Promise<ShopStatusData> {
  return service.get('/company/Dashboard/shopStatus')
}
