import { ref, nextTick } from 'vue'
import type { ConfirmDialogProps } from '@/components/ConfirmDialog.vue'

// 确认对话框状态
const confirmDialogState = ref({
  visible: false,
  type: 'warning' as ConfirmDialogProps['type'],
  title: '确认操作',
  message: '',
  details: '',
  confirmText: '确定',
  cancelText: '取消',
  loadingText: '处理中...',
  loading: false
})

// 消息提示状态
const messageToastRef = ref<any>(null)

// 确认对话框的 Promise 解析器
let confirmResolver: ((value: boolean) => void) | null = null

/**
 * 显示确认对话框
 */
export const useConfirm = () => {
  const showConfirm = (options: {
    type?: ConfirmDialogProps['type']
    title?: string
    message: string
    details?: string
    confirmText?: string
    cancelText?: string
  }): Promise<boolean> => {
    return new Promise((resolve) => {
      confirmDialogState.value = {
        visible: true,
        type: options.type || 'warning',
        title: options.title || '确认操作',
        message: options.message,
        details: options.details || '',
        confirmText: options.confirmText || '确定',
        cancelText: options.cancelText || '取消',
        loadingText: '处理中...',
        loading: false
      }
      
      confirmResolver = resolve
    })
  }

  const handleConfirm = async () => {
    if (confirmResolver) {
      // 显示加载状态
      confirmDialogState.value.loading = true
      
      // 模拟异步操作
      await nextTick()
      
      confirmDialogState.value.visible = false
      confirmDialogState.value.loading = false
      confirmResolver(true)
      confirmResolver = null
    }
  }

  const handleCancel = () => {
    if (confirmResolver) {
      confirmDialogState.value.visible = false
      confirmDialogState.value.loading = false
      confirmResolver(false)
      confirmResolver = null
    }
  }

  return {
    confirmDialogState,
    showConfirm,
    handleConfirm,
    handleCancel
  }
}

/**
 * 显示消息提示
 */
export const useMessage = () => {
  const setMessageToastRef = (ref: any) => {
    messageToastRef.value = ref
  }

  const success = (message: string, title?: string) => {
    if (messageToastRef.value) {
      messageToastRef.value.success(message, title)
    }
  }

  const error = (message: string, title?: string) => {
    if (messageToastRef.value) {
      messageToastRef.value.error(message, title)
    }
  }

  const warning = (message: string, title?: string) => {
    if (messageToastRef.value) {
      messageToastRef.value.warning(message, title)
    }
  }

  const info = (message: string, title?: string) => {
    if (messageToastRef.value) {
      messageToastRef.value.info(message, title)
    }
  }

  return {
    setMessageToastRef,
    success,
    error,
    warning,
    info
  }
}

/**
 * 便捷的确认删除对话框
 */
export const useDeleteConfirm = () => {
  const { showConfirm } = useConfirm()

  const confirmDelete = (itemName: string, itemType: string = '项目'): Promise<boolean> => {
    return showConfirm({
      type: 'danger',
      title: '确认删除',
      message: `确定要删除${itemType}"${itemName}"吗？`,
      details: '此操作不可恢复，请谨慎操作。',
      confirmText: '删除',
      cancelText: '取消'
    })
  }

  return {
    confirmDelete
  }
}

/**
 * 便捷的状态切换确认对话框
 */
export const useStatusConfirm = () => {
  const { showConfirm } = useConfirm()

  const confirmStatusChange = (
    itemName: string, 
    action: string, 
    itemType: string = '项目'
  ): Promise<boolean> => {
    return showConfirm({
      type: 'warning',
      title: '确认操作',
      message: `确定要${action}${itemType}"${itemName}"吗？`,
      confirmText: action,
      cancelText: '取消'
    })
  }

  return {
    confirmStatusChange
  }
}
