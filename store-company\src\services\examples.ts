/**
 * API使用示例文件
 * 展示如何在Vue组件中使用封装的API服务
 */

import { 
  // 认证相关
  getUserInfo, 
  logout,
  
  // 管理员相关
  getAdminList,
  getAdminDetail,
  addAdmin,
  updateAdmin,
  deleteAdmin,
  toggleAdminStatus,
  
  // 角色相关
  getRoleList,
  getRoleDetail,
  addRole,
  updateRole,
  deleteRole,
  getPermissionTree,
  getRoleOptions,
  
  // 门店相关
  getShopList,
  getShopDetail,
  addShop,
  updateShop,
  deleteShop,
  getShopOptions,
  
  // 基础API工具
  getList,
  getDetail,
  create,
  update,
  remove
} from '@/services'

// 单独导入 login 函数以避免冲突
import { login } from '@/services/authApi'

// 在Vue组件中的使用示例：

/**
 * 1. 管理员列表页面示例
 */
export const adminListExample = async () => {
  try {
    // 获取管理员列表
    const adminList = await getAdminList({
      page: 1,
      page_size: 10,
      username: 'admin', // 可选的搜索条件
      status: 1 // 可选的状态筛选
    })
    
    console.log('管理员列表:', adminList)
    // adminList.itemList - 数据列表
    // adminList.total - 总数
    // adminList.pageSize - 每页数量
    
  } catch (error) {
    console.error('获取管理员列表失败:', error)
  }
}

/**
 * 2. 管理员详情页面示例
 */
export const adminDetailExample = async (id: number) => {
  try {
    const adminDetail = await getAdminDetail(id)
    console.log('管理员详情:', adminDetail)
  } catch (error) {
    console.error('获取管理员详情失败:', error)
  }
}

/**
 * 3. 新增管理员示例
 */
export const addAdminExample = async () => {
  try {
    const result = await addAdmin({
      username: 'newadmin',
      nickname: '新管理员',
      password: '123456',
      mobile: '***********',
      role_id: 2,
      status: 1
    })
    
    console.log('新增管理员成功:', result)
  } catch (error) {
    console.error('新增管理员失败:', error)
  }
}

/**
 * 4. 更新管理员示例
 */
export const updateAdminExample = async () => {
  try {
    const result = await updateAdmin({
      id: 1,
      username: 'admin',
      nickname: '更新后的管理员',
      mobile: '13800138001',
      role_id: 1,
      status: 1
    })
    
    console.log('更新管理员成功:', result)
  } catch (error) {
    console.error('更新管理员失败:', error)
  }
}

/**
 * 5. 删除管理员示例
 */
export const deleteAdminExample = async (id: number) => {
  try {
    const result = await deleteAdmin(id)
    console.log('删除管理员成功:', result)
  } catch (error) {
    console.error('删除管理员失败:', error)
  }
}

/**
 * 6. 角色管理示例
 */
export const roleManagementExample = async () => {
  try {
    // 获取角色列表
    const roleList = await getRoleList({
      page: 1,
      page_size: 10
    })
    
    // 获取权限树
    const permissionTree = await getPermissionTree()
    
    // 获取角色选项（用于下拉选择）
    const roleOptions = await getRoleOptions()
    
    console.log('角色列表:', roleList)
    console.log('权限树:', permissionTree)
    console.log('角色选项:', roleOptions)
    
  } catch (error) {
    console.error('角色管理操作失败:', error)
  }
}

/**
 * 7. 门店管理示例
 */
export const shopManagementExample = async () => {
  try {
    // 获取门店列表
    const shopList = await getShopList({
      page: 1,
      page_size: 10,
      shop_name: '测试门店' // 可选的搜索条件
    })
    
    // 新增门店
    const newShop = await addShop({
      shop_name: '新门店',
      contact_person: '张三',
      contact_phone: '***********',
      address: '北京市朝阳区',
      business_hours: '09:00-21:00',
      description: '门店描述',
      status: 1
    })
    
    console.log('门店列表:', shopList)
    console.log('新增门店:', newShop)
    
  } catch (error) {
    console.error('门店管理操作失败:', error)
  }
}

/**
 * 8. 使用基础API工具的示例
 */
export const baseApiExample = async () => {
  try {
    // 使用通用的列表查询方法
    const customList = await getList('/company/CustomModule/index', {
      page: 1,
      page_size: 10,
      custom_field: 'value'
    })
    
    // 使用通用的详情查询方法
    const customDetail = await getDetail('/company/CustomModule/edit', 1)
    
    // 使用通用的新增方法
    const createResult = await create('/company/CustomModule/addPost', {
      name: '测试数据',
      status: 1
    })
    
    console.log('自定义列表:', customList)
    console.log('自定义详情:', customDetail)
    console.log('创建结果:', createResult)
    
  } catch (error) {
    console.error('基础API操作失败:', error)
  }
}

/**
 * 9. 在Vue组件中的完整使用示例
 */
/*
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getAdminList, addAdmin, type Admin, type AdminListParams } from '@/services'
import { ElMessage } from 'element-plus'

// 响应式数据
const adminList = ref<Admin[]>([])
const total = ref(0)
const loading = ref(false)
const searchParams = ref<AdminListParams>({
  page: 1,
  page_size: 10
})

// 获取管理员列表
const fetchAdminList = async () => {
  try {
    loading.value = true
    const result = await getAdminList(searchParams.value)
    adminList.value = result.itemList
    total.value = result.total
  } catch (error) {
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 新增管理员
const handleAddAdmin = async (formData: any) => {
  try {
    await addAdmin(formData)
    ElMessage.success('新增管理员成功')
    fetchAdminList() // 刷新列表
  } catch (error) {
    // 错误消息已在响应拦截器中处理
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAdminList()
})
</script>
*/
