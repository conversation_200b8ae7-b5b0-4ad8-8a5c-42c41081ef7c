<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.51">
    <root id="1"/>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8_tolower_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="272" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="273" parent="1" name="eric_sass-api_com">
      <LastIntrospectionLocalTimestamp>2025-02-10.01:44:19</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="274" parent="1" name="eric_sass-api_com">
      <Host>localhost</Host>
    </user>
    <table id="275" parent="273" name="cache">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="276" parent="273" name="cache_locks">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="277" parent="273" name="cs_admin_roles">
      <Comment>平台管理员角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="278" parent="273" name="cs_admins">
      <Comment>平台管理员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="279" parent="273" name="cs_company_admin_roles">
      <Comment>公司管理员角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="280" parent="273" name="cs_company_admins">
      <Comment>公司管理员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="281" parent="273" name="cs_companys">
      <Comment>公司表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="282" parent="273" name="cs_menus">
      <Comment>系统菜单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="283" parent="273" name="cs_shop_admin_roles">
      <Comment>门店管理员角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="284" parent="273" name="cs_shop_admins">
      <Comment>门店管理员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="285" parent="273" name="cs_shop_settings">
      <Comment>门店设置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="286" parent="273" name="cs_shops">
      <Comment>门店表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="287" parent="273" name="cs_trades">
      <Comment>平台管理员角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="288" parent="273" name="cs_user_levels">
      <Comment>会员等级表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="289" parent="273" name="cs_user_recharge_packages">
      <Comment>充值套餐表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="290" parent="273" name="failed_jobs">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="291" parent="273" name="job_batches">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="292" parent="273" name="jobs">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="293" parent="273" name="migrations">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="294" parent="273" name="password_reset_tokens">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="295" parent="273" name="personal_access_tokens">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="296" parent="273" name="sessions">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="297" parent="273" name="users">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="298" parent="275" name="key">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="299" parent="275" name="value">
      <DasType>mediumtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="300" parent="275" name="expiration">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="301" parent="275" name="PRIMARY">
      <ColNames>key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="302" parent="275" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="303" parent="276" name="key">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="304" parent="276" name="owner">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="305" parent="276" name="expiration">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="306" parent="276" name="PRIMARY">
      <ColNames>key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="307" parent="276" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="308" parent="277" name="id">
      <AutoIncrement>17</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="309" parent="277" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="310" parent="277" name="role_permission">
      <Comment>角色权限</Comment>
      <DasType>longtext|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="311" parent="277" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="312" parent="277" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="313" parent="277" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="314" parent="277" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="315" parent="277" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="316" parent="278" name="id">
      <AutoIncrement>6</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="317" parent="278" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="318" parent="278" name="username">
      <Comment>登录账号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="319" parent="278" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="320" parent="278" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="321" parent="278" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="322" parent="278" name="login_ip">
      <Comment>登录IP</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="323" parent="278" name="login_time">
      <Comment>登录时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="324" parent="278" name="status">
      <Comment>状态，0：禁用，1：正常</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="325" parent="278" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="326" parent="278" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="327" parent="278" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <foreign-key id="328" parent="278" name="cs_admins_role_id_foreign">
      <ColNames>role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_admin_roles</RefTableName>
    </foreign-key>
    <index id="329" parent="278" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="330" parent="278" name="cs_admins_role_id_foreign">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="331" parent="278" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="332" parent="279" name="id">
      <AutoIncrement>10</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="333" parent="279" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="334" parent="279" name="role_permission">
      <Comment>角色权限</Comment>
      <DasType>longtext|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="335" parent="279" name="company_id">
      <Comment>公司ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="336" parent="279" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="337" parent="279" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="338" parent="279" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <foreign-key id="339" parent="279" name="cs_company_admin_roles_company_id_foreign">
      <ColNames>company_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_companys</RefTableName>
    </foreign-key>
    <index id="340" parent="279" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="341" parent="279" name="cs_company_admin_roles_company_id_foreign">
      <ColNames>company_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="342" parent="279" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="343" parent="280" name="id">
      <AutoIncrement>11</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="344" parent="280" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="345" parent="280" name="username">
      <Comment>登录账号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="346" parent="280" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="347" parent="280" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="348" parent="280" name="company_id">
      <Comment>公司ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="349" parent="280" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="350" parent="280" name="login_ip">
      <Comment>登录IP</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="351" parent="280" name="login_time">
      <Comment>登录时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="352" parent="280" name="status">
      <Comment>状态，0：禁用，1：正常</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="353" parent="280" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="354" parent="280" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="355" parent="280" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="356" parent="280" name="is_origin">
      <Comment>是否是原始管理员</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <foreign-key id="357" parent="280" name="cs_company_admins_company_id_foreign">
      <ColNames>company_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_companys</RefTableName>
    </foreign-key>
    <foreign-key id="358" parent="280" name="cs_company_admins_role_id_foreign">
      <ColNames>role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_company_admin_roles</RefTableName>
    </foreign-key>
    <index id="359" parent="280" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="360" parent="280" name="cs_company_admins_company_id_foreign">
      <ColNames>company_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="361" parent="280" name="cs_company_admins_role_id_foreign">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="362" parent="280" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="363" parent="281" name="id">
      <AutoIncrement>13</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="364" parent="281" name="company_name">
      <Comment>公司名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="365" parent="281" name="mobile">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="366" parent="281" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="367" parent="281" name="status">
      <Comment>状态：0-禁用，1-启用</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="368" parent="281" name="expired_at">
      <Comment>到期时间</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="369" parent="281" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="370" parent="281" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="371" parent="281" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="372" parent="281" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="373" parent="281" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="374" parent="282" name="id">
      <AutoIncrement>9</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="375" parent="282" name="pid">
      <Comment>上级菜单主键</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="376" parent="282" name="menu_name">
      <Comment>菜单名称</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="377" parent="282" name="menu_img">
      <Comment>菜单图标</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="378" parent="282" name="menu_sign">
      <Comment>菜单标识</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="379" parent="282" name="menu_type">
      <Comment>菜单类型，0：平台；1：授权商户；2：门店；</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="380" parent="282" name="is_show">
      <Comment>菜单状态，0：不作为菜单；1：作为菜单。</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="381" parent="282" name="sort">
      <Comment>排序</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>50</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="382" parent="282" name="status">
      <Comment>状态</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="383" parent="282" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="384" parent="282" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="385" parent="282" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="386" parent="282" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="387" parent="282" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="388" parent="283" name="id">
      <AutoIncrement>8</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="389" parent="283" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="390" parent="283" name="role_permission">
      <Comment>角色权限</Comment>
      <DasType>longtext|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="391" parent="283" name="shop_id">
      <Comment>门店ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="392" parent="283" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="393" parent="283" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="394" parent="283" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <foreign-key id="395" parent="283" name="cs_shop_admin_roles_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_shops</RefTableName>
    </foreign-key>
    <index id="396" parent="283" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="397" parent="283" name="cs_shop_admin_roles_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="398" parent="283" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="399" parent="284" name="id">
      <AutoIncrement>6</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="400" parent="284" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="401" parent="284" name="username">
      <Comment>登录账号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="402" parent="284" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="403" parent="284" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="404" parent="284" name="shop_id">
      <Comment>门店ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="405" parent="284" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="406" parent="284" name="login_ip">
      <Comment>登录IP</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="407" parent="284" name="login_time">
      <Comment>登录时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="408" parent="284" name="status">
      <Comment>状态，0：禁用，1：正常</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="409" parent="284" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="410" parent="284" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="411" parent="284" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="412" parent="284" name="is_origin">
      <Comment>是否是原始管理员</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <foreign-key id="413" parent="284" name="cs_shop_admins_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_shops</RefTableName>
    </foreign-key>
    <foreign-key id="414" parent="284" name="cs_shop_admins_role_id_foreign">
      <ColNames>role_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_shop_admin_roles</RefTableName>
    </foreign-key>
    <index id="415" parent="284" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="416" parent="284" name="cs_shop_admins_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="417" parent="284" name="cs_shop_admins_role_id_foreign">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="418" parent="284" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="419" parent="285" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="420" parent="285" name="rule_name">
      <Comment>规则名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="421" parent="285" name="rule_value">
      <Comment>规则值</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="422" parent="285" name="shop_id">
      <Comment>门店ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="423" parent="285" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="424" parent="285" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="425" parent="285" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <foreign-key id="426" parent="285" name="cs_shop_settings_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_shops</RefTableName>
    </foreign-key>
    <index id="427" parent="285" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="428" parent="285" name="cs_shop_settings_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="429" parent="285" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="430" parent="286" name="id">
      <AutoIncrement>7</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="431" parent="286" name="shop_name">
      <Comment>店名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="432" parent="286" name="mobile">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="433" parent="286" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="434" parent="286" name="status">
      <Comment>状态：0-暂停营业，1-正常营业</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="435" parent="286" name="company_id">
      <Comment>公司ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="436" parent="286" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="437" parent="286" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="438" parent="286" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <foreign-key id="439" parent="286" name="cs_shops_company_id_foreign">
      <ColNames>company_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_companys</RefTableName>
    </foreign-key>
    <index id="440" parent="286" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="441" parent="286" name="cs_shops_company_id_foreign">
      <ColNames>company_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="442" parent="286" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="443" parent="287" name="id">
      <AutoIncrement>7</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="444" parent="287" name="trade_name">
      <Comment>行业名称</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="445" parent="287" name="sort">
      <Comment>排序</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>50</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="446" parent="287" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="447" parent="287" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="448" parent="287" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="449" parent="287" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="450" parent="287" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="451" parent="288" name="id">
      <AutoIncrement>4</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="452" parent="288" name="user_level_name">
      <Comment>会员等级名称</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="453" parent="288" name="goods_discount">
      <Comment>商品折扣</Comment>
      <DasType>decimal(11,2 digit)|0s</DasType>
      <DefaultExpression>10.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="454" parent="288" name="room_discount">
      <Comment>包间折扣</Comment>
      <DasType>decimal(11,2 digit)|0s</DasType>
      <DefaultExpression>10.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="455" parent="288" name="service_discount">
      <Comment>服务费折扣</Comment>
      <DasType>decimal(11,2 digit)|0s</DasType>
      <DefaultExpression>10.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="456" parent="288" name="shop_id">
      <Comment>门店ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="457" parent="288" name="can_not_delete">
      <Comment>禁止删除</Comment>
      <DasType>tinyint(3) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="458" parent="288" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="459" parent="288" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="460" parent="288" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <foreign-key id="461" parent="288" name="cs_user_levels_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_shops</RefTableName>
    </foreign-key>
    <index id="462" parent="288" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="463" parent="288" name="cs_user_levels_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="464" parent="288" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="465" parent="289" name="id">
      <AutoIncrement>7</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="466" parent="289" name="package_name">
      <Comment>套餐名称</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="467" parent="289" name="recharge_amount">
      <Comment>充值金额</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="468" parent="289" name="gift_amount">
      <Comment>充值金额</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="469" parent="289" name="shop_id">
      <Comment>门店ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="470" parent="289" name="created_at">
      <Comment>添加时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="471" parent="289" name="updated_at">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="472" parent="289" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <foreign-key id="473" parent="289" name="cs_user_recharge_packages_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>cs_shops</RefTableName>
    </foreign-key>
    <index id="474" parent="289" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="475" parent="289" name="cs_user_recharge_packages_shop_id_foreign">
      <ColNames>shop_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="476" parent="289" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="477" parent="290" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="478" parent="290" name="uuid">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="479" parent="290" name="connection">
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="480" parent="290" name="queue">
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="481" parent="290" name="payload">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="482" parent="290" name="exception">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="483" parent="290" name="failed_at">
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="484" parent="290" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="485" parent="290" name="failed_jobs_uuid_unique">
      <ColNames>uuid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="486" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="487" parent="290" name="failed_jobs_uuid_unique">
      <UnderlyingIndexName>failed_jobs_uuid_unique</UnderlyingIndexName>
    </key>
    <column id="488" parent="291" name="id">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="489" parent="291" name="name">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="490" parent="291" name="total_jobs">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="491" parent="291" name="pending_jobs">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="492" parent="291" name="failed_jobs">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="493" parent="291" name="failed_job_ids">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="494" parent="291" name="options">
      <DasType>mediumtext|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="495" parent="291" name="cancelled_at">
      <DasType>int(11)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="496" parent="291" name="created_at">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="497" parent="291" name="finished_at">
      <DasType>int(11)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="498" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="499" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="500" parent="292" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="501" parent="292" name="queue">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="502" parent="292" name="payload">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="503" parent="292" name="attempts">
      <DasType>tinyint(3) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="504" parent="292" name="reserved_at">
      <DasType>int(10) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="505" parent="292" name="available_at">
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="506" parent="292" name="created_at">
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="507" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="508" parent="292" name="jobs_queue_index">
      <ColNames>queue</ColNames>
      <Type>btree</Type>
    </index>
    <key id="509" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="510" parent="293" name="id">
      <AutoIncrement>29</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="511" parent="293" name="migration">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="512" parent="293" name="batch">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="513" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="514" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="515" parent="294" name="email">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="516" parent="294" name="token">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="517" parent="294" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="518" parent="294" name="PRIMARY">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="519" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="520" parent="295" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="521" parent="295" name="tokenable_type">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="522" parent="295" name="tokenable_id">
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="523" parent="295" name="name">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="524" parent="295" name="token">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="525" parent="295" name="abilities">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="526" parent="295" name="last_used_at">
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="527" parent="295" name="expires_at">
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="528" parent="295" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="529" parent="295" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="530" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="531" parent="295" name="personal_access_tokens_token_unique">
      <ColNames>token</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="532" parent="295" name="personal_access_tokens_tokenable_type_tokenable_id_index">
      <ColNames>tokenable_type
tokenable_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="533" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="534" parent="295" name="personal_access_tokens_token_unique">
      <UnderlyingIndexName>personal_access_tokens_token_unique</UnderlyingIndexName>
    </key>
    <column id="535" parent="296" name="id">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="536" parent="296" name="user_id">
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="537" parent="296" name="ip_address">
      <DasType>varchar(45)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="538" parent="296" name="user_agent">
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="539" parent="296" name="payload">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="540" parent="296" name="last_activity">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="541" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="542" parent="296" name="sessions_user_id_index">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="543" parent="296" name="sessions_last_activity_index">
      <ColNames>last_activity</ColNames>
      <Type>btree</Type>
    </index>
    <key id="544" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="545" parent="297" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="546" parent="297" name="name">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="547" parent="297" name="email">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="548" parent="297" name="email_verified_at">
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="549" parent="297" name="password">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="550" parent="297" name="remember_token">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="551" parent="297" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="552" parent="297" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="553" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="554" parent="297" name="users_email_unique">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="555" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="556" parent="297" name="users_email_unique">
      <UnderlyingIndexName>users_email_unique</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>