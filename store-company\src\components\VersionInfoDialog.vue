<template>
  <div class="version-info-dialog">
    <el-dialog
      v-model="visible"
      title="企业版本信息"
      width="800px"
      center
      @close="handleClose"
    >
      <div class="version-content" v-if="userInfo">
        <!-- 企业基本信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <el-icon><InfoFilled /></el-icon>
            企业信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">企业名称：</span>
              <span class="value">{{ userInfo.company_info.company_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系手机：</span>
              <span class="value">{{ userInfo.company_info.mobile }}</span>
            </div>
            <div class="info-item">
              <span class="label">企业状态：</span>
              <el-tag :type="userInfo.company_info.status === 1 ? 'success' : 'danger'">
                {{ userInfo.company_info.status === 1 ? '正常' : '已冻结' }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">门店数量：</span>
              <span class="value">{{ userInfo.company_info.shop_nums }} 个</span>
            </div>
          </div>
        </div>

        <!-- 套餐版本信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <el-icon><StarFilled /></el-icon>
            套餐版本
          </h3>
          <div class="version-card">
            <div class="version-header">
              <h4 class="version-name">{{ userInfo.version_info.version_name }}</h4>
              <div class="version-status">
                <el-tag 
                  :type="userInfo.company_info.is_expired ? 'danger' : 'success'"
                  size="large"
                >
                  {{ userInfo.company_info.is_expired ? '已到期' : '正常使用中' }}
                </el-tag>
              </div>
            </div>
            
            <div class="version-details">
              <div class="detail-item">
                <span class="detail-label">套餐价格：</span>
                <span class="detail-value price">¥{{ userInfo.version_info.product_price }}/年</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">续费价格：</span>
                <span class="detail-value price">¥{{ userInfo.version_info.continue_price }}/年</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">门店限制：</span>
                <span class="detail-value">最多 {{ userInfo.version_info.limit_nums }} 个门店</span>
              </div>
              <div class="detail-item" v-if="userInfo.version_info.remark">
                <span class="detail-label">套餐说明：</span>
                <span class="detail-value">{{ userInfo.version_info.remark }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用期限信息 -->
        <div class="info-section">
          <h3 class="section-title">
            <el-icon><Calendar /></el-icon>
            使用期限
          </h3>
          <div class="time-info">
            <div class="time-item">
              <span class="time-label">购买日期：</span>
              <span class="time-value">{{ userInfo.company_info.buy_at }}</span>
            </div>
            <div class="time-item">
              <span class="time-label">到期日期：</span>
              <span class="time-value" :class="{ 'expired': userInfo.company_info.is_expired }">
                {{ userInfo.company_info.expired_at }}
              </span>
            </div>
            <div class="time-item">
              <span class="time-label">购买年限：</span>
              <span class="time-value">{{ userInfo.company_info.buy_year }} 年</span>
            </div>
            <div class="time-item">
              <span class="time-label">剩余天数：</span>
              <span class="time-value" :class="getRemainingDaysClass()">
                {{ userInfo.company_info.remaining_days }} 天
              </span>
            </div>
          </div>
        </div>

        <!-- 续费提醒 -->
        <div class="renewal-notice" v-if="userInfo.company_info.remaining_days <= 30">
          <el-alert
            :title="getRenewalMessage()"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, StarFilled, Calendar } from '@element-plus/icons-vue'
import type { UserInfo } from '@/types'

interface Props {
  modelValue: boolean
  userInfo: UserInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  visible.value = false
}

const getRemainingDaysClass = () => {
  if (!props.userInfo) return ''
  const days = props.userInfo.company_info.remaining_days
  if (days <= 0) return 'expired'
  if (days <= 7) return 'critical'
  if (days <= 30) return 'warning'
  return 'normal'
}

const getRenewalMessage = () => {
  if (!props.userInfo) return ''
  const days = props.userInfo.company_info.remaining_days
  if (days <= 0) return '您的套餐已到期，请立即联系客服续费以避免影响正常使用'
  if (days <= 7) return `您的套餐将在 ${days} 天后到期，请及时续费`
  if (days <= 30) return `您的套餐将在 ${days} 天后到期，建议提前续费`
  return ''
}

</script>

<style scoped>
.version-content {
  /* 移除最大高度和滚动条限制 */
}

.info-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 2px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
}

.label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
}

.value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.version-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.version-name {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.version-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-label {
  opacity: 0.9;
}

.detail-value {
  font-weight: 600;
}

.detail-value.price {
  color: #ffd700;
  font-size: 16px;
}

.time-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-primary);
}

.time-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.time-value {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.time-value.expired {
  color: var(--el-color-danger);
}

.time-value.critical {
  color: var(--el-color-danger);
  animation: blink 1s infinite;
}

.time-value.warning {
  color: var(--el-color-warning);
}

.time-value.normal {
  color: var(--el-color-success);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

.renewal-notice {
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid,
  .version-details,
  .time-info {
    grid-template-columns: 1fr;
  }
  
  .version-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>