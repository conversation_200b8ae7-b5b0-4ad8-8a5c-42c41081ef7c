<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">数据大屏</h1>
      <p class="page-description">实时监控所有门店的关键经营数据</p>
    </div>

    <!-- 核心指标卡片区域 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon shops">🏬</div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardData.totalShops }}</div>
          <div class="stat-label">门店总数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon revenue">💸</div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatNumber(dashboardData.todayRevenue) }}</div>
          <div class="stat-label">今日营业额</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon orders">📋</div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardData.todayOrders }}</div>
          <div class="stat-label">今日订单</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon members">👤</div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(dashboardData.activeMembers) }}</div>
          <div class="stat-label">活跃会员</div>
        </div>
      </div>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-grid">
      <!-- 营业额趋势图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">营业额趋势</h3>
          <div class="time-selector">
            <button class="time-btn" :class="{ active: chartTimeRange === 'today' }" @click="switchTimeRange('today')">今日</button>
            <button class="time-btn" :class="{ active: chartTimeRange === 'week' }" @click="switchTimeRange('week')">本周</button>
            <button class="time-btn" :class="{ active: chartTimeRange === 'month' }" @click="switchTimeRange('month')">本月</button>
            <button class="time-btn" :class="{ active: chartTimeRange === 'year' }" @click="switchTimeRange('year')">本年</button>
          </div>
        </div>
        <div class="chart-body">
          <div class="chart-container" ref="revenueChartRef">
          </div>
        </div>
      </div>

      <!-- 门店业绩对比 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">门店业绩对比</h3>
          <div class="time-selector">
            <button class="time-btn" :class="{ active: shopCompareTimeRange === 'today' }" @click="switchShopCompareTimeRange('today')">今日</button>
            <button class="time-btn" :class="{ active: shopCompareTimeRange === 'week' }" @click="switchShopCompareTimeRange('week')">本周</button>
            <button class="time-btn" :class="{ active: shopCompareTimeRange === 'month' }" @click="switchShopCompareTimeRange('month')">本月</button>
            <button class="time-btn" :class="{ active: shopCompareTimeRange === 'year' }" @click="switchShopCompareTimeRange('year')">本年</button>
          </div>
        </div>
        <div class="chart-body">
          <div class="chart-container" ref="shopCompareChartRef">
          </div>
        </div>
      </div>

      <!-- 支付方式分析 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">支付方式分析</h3>
          <div class="time-selector">
            <button class="time-btn" :class="{ active: paymentTimeRange === 'today' }" @click="switchPaymentTimeRange('today')">今日</button>
            <button class="time-btn" :class="{ active: paymentTimeRange === 'week' }" @click="switchPaymentTimeRange('week')">本周</button>
            <button class="time-btn" :class="{ active: paymentTimeRange === 'month' }" @click="switchPaymentTimeRange('month')">本月</button>
            <button class="time-btn" :class="{ active: paymentTimeRange === 'year' }" @click="switchPaymentTimeRange('year')">本年</button>
          </div>
        </div>
        <div class="chart-body">
          <div class="chart-container" ref="paymentChartRef">
          </div>
        </div>
      </div>

      <!-- 热销商品排行 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">热销商品排行</h3>
          <div class="time-selector">
            <button class="time-btn" :class="{ active: productRankTimeRange === 'today' }" @click="switchProductRankTimeRange('today')">今日</button>
            <button class="time-btn" :class="{ active: productRankTimeRange === 'week' }" @click="switchProductRankTimeRange('week')">本周</button>
            <button class="time-btn" :class="{ active: productRankTimeRange === 'month' }" @click="switchProductRankTimeRange('month')">本月</button>
            <button class="time-btn" :class="{ active: productRankTimeRange === 'year' }" @click="switchProductRankTimeRange('year')">本年</button>
          </div>
        </div>
        <div class="chart-body">
          <div class="chart-container" ref="productRankChartRef">
          </div>
        </div>
      </div>
    </div>

    <!-- 门店状态表格 -->
    <div class="table-card">
      <div class="table-header">
        <h3 class="table-title">门店实时状态</h3>
        <div class="table-actions">
          <button class="refresh-btn" @click="refreshShopData">刷新</button>
        </div>
      </div>
      <div class="table-body">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>门店名称</th>
                <th>运营状态</th>
                <th>今日营业额</th>
                <th>订单数</th>
                <th>客单价</th>
                <th>利润</th>
                <th>库存状态</th>
                <th>最后更新</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="shop in dashboardData.shopList" :key="shop.id" :class="{ 'row-offline': shop.status === 'offline' }">
                <td>
                  <div class="shop-name">
                    <span class="name">{{ shop.name }}</span>
                    <span class="rank" v-if="getShopRank(shop.todayRevenue) <= 3">#{{ getShopRank(shop.todayRevenue) }}</span>
                  </div>
                </td>
                <td>
                  <span class="status-tag" :class="shop.status">
                    <span class="status-dot"></span>
                    {{ shop.status === 'online' ? '营业中' : '已打烊' }}
                  </span>
                </td>
                <td class="revenue-cell">
                  <span class="amount">¥{{ formatNumber(shop.todayRevenue) }}</span>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: (shop.todayRevenue / 50000 * 100) + '%' }"></div>
                  </div>
                </td>
                <td>{{ shop.todayOrders }}</td>
                <td>¥{{ shop.avgPrice }}</td>
                <td class="profit-cell">
                  <span class="profit-amount">¥{{ formatNumber(shop.profit) }}</span>
                  <span class="profit-rate">({{ shop.todayRevenue > 0 ? ((shop.profit / shop.todayRevenue) * 100).toFixed(1) : 0 }}%)</span>
                </td>
                <td>
                  <span class="stock-status" :class="shop.stockStatus">
                    {{ getStockStatusText(shop.stockStatus) }}
                  </span>
                </td>
                <td class="update-time">{{ shop.lastUpdate }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  getDashboardOverview,
  getRevenueTrend,
  getShopComparison,
  getPaymentAnalysis,
  getProductRanking,
  getShopStatus
} from '@/services/dashboardApi'
import type {
  DashboardOverview,
  RevenueTrendData,
  ShopComparisonData,
  PaymentAnalysisData,
  ProductRankingData,
  ShopStatusData
} from '@/services/dashboardApi'

const chartTimeRange = ref('today')
const shopCompareTimeRange = ref('today')
const paymentTimeRange = ref('today')
const productRankTimeRange = ref('today')

// 图表引用
const revenueChartRef = ref<HTMLElement | null>(null)
const shopCompareChartRef = ref<HTMLElement | null>(null)
const paymentChartRef = ref<HTMLElement | null>(null)
const productRankChartRef = ref<HTMLElement | null>(null)

// 图表实例
let revenueChart: echarts.ECharts | null = null
let shopCompareChart: echarts.ECharts | null = null
let paymentChart: echarts.ECharts | null = null
let productRankChart: echarts.ECharts | null = null

// 响应式数据
const dashboardData = reactive({
  totalShops: 0,
  onlineShops: 0,
  newShopsThisMonth: 0,
  todayRevenue: 0,
  todayOrders: 0,
  successOrders: 0,
  activeMembers: 0,
  shopList: [] as any[]
})

// 加载状态
const loading = ref(false)



const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getStockStatusText = (status: string) => {
  const statusMap = {
    normal: '正常',
    warning: '预警',
    low: '不足'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

const getShopRank = (revenue: number) => {
  const sortedShops = [...dashboardData.shopList].sort((a, b) => b.todayRevenue - a.todayRevenue)
  return sortedShops.findIndex(shop => shop.todayRevenue === revenue) + 1
}

const refreshShopData = async () => {
  await loadShopStatusData()
}

// 加载概览数据
const loadOverviewData = async () => {
  try {
    const data = await getDashboardOverview()
    Object.assign(dashboardData, data)
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}

// 加载门店状态数据
const loadShopStatusData = async () => {
  try {
    const data = await getShopStatus()
    dashboardData.shopList = data.shopList
  } catch (error) {
    console.error('加载门店状态数据失败:', error)
  }
}

const switchTimeRange = async (range: string) => {
  chartTimeRange.value = range
  await loadRevenueData()
  updateRevenueChart()
}

const switchShopCompareTimeRange = async (range: string) => {
  shopCompareTimeRange.value = range
  await loadShopCompareData()
  updateShopCompareChart()
}

const switchPaymentTimeRange = async (range: string) => {
  paymentTimeRange.value = range
  await loadPaymentData()
  updatePaymentChart()
}

const switchProductRankTimeRange = async (range: string) => {
  productRankTimeRange.value = range
  await loadProductRankData()
  updateProductRankChart()
}


// 营业额趋势图数据
const revenueData = ref<RevenueTrendData>({ period: 'today', xAxis: [], data: [] })

const loadRevenueData = async () => {
  try {
    const data = await getRevenueTrend(chartTimeRange.value)
    revenueData.value = data
  } catch (error) {
    console.error('加载营业额趋势数据失败:', error)
    // 使用默认数据
    const timeRanges = {
      today: {
        xAxis: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
        data: [0, 2500, 8600, 15200, 22800, 18900]
      },
      week: {
        xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [85600, 92400, 78900, 95200, 102500, 125680, 110200]
      },
      month: {
        xAxis: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
        data: [125680, 145200, 132400, 156800, 178900, 165400, 189200]
      },
      year: {
        xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        data: [1256800, 1452000, 1324000, 1568000, 1789000, 1654000, 1892000, 2104000, 1987000, 2156000, 2234000, 2456000]
      }
    }
    revenueData.value = {
      period: chartTimeRange.value,
      ...timeRanges[chartTimeRange.value as keyof typeof timeRanges]
    }
  }
}

// 门店对比数据
const shopCompareData = ref<ShopComparisonData>({ period: 'today', shopNames: [], revenues: [] })

const loadShopCompareData = async () => {
  try {
    const data = await getShopComparison(shopCompareTimeRange.value)
    shopCompareData.value = data
  } catch (error) {
    console.error('加载门店对比数据失败:', error)
    // 使用默认数据
    const timeRanges = {
      today: {
        shopNames: ['旗舰店', '商场店', '社区店', '写字楼店'],
        revenues: [35600, 28900, 24580, 19240]
      },
      week: {
        shopNames: ['旗舰店', '商场店', '社区店', '写字楼店'],
        revenues: [245600, 198900, 165580, 129240]
      },
      month: {
        shopNames: ['旗舰店', '商场店', '社区店', '写字楼店'],
        revenues: [1045600, 898900, 765580, 629240]
      },
      year: {
        shopNames: ['旗舰店', '商场店', '社区店', '写字楼店'],
        revenues: [12545600, 10898900, 9765580, 8629240]
      }
    }
    shopCompareData.value = {
      period: shopCompareTimeRange.value,
      ...timeRanges[shopCompareTimeRange.value as keyof typeof timeRanges]
    }
  }
}

// 支付方式数据
const paymentData = ref<PaymentAnalysisData>({
  period: 'today',
  total_amount: 0,
  payment_methods: []
})

const loadPaymentData = async () => {
  try {
    const data = await getPaymentAnalysis(paymentTimeRange.value)
    paymentData.value = data
  } catch (error) {
    console.error('加载支付方式数据失败:', error)
    // 使用默认数据
    paymentData.value = {
      period: paymentTimeRange.value,
      total_amount: 125680,
      payment_methods: [
        { id: 1, name: '微信支付', amount: 56807, percentage: 45.2, count: 154 },
        { id: 2, name: '支付宝', amount: 41223, percentage: 32.8, count: 98 },
        { id: 3, name: '现金支付', amount: 19606, percentage: 15.6, count: 67 },
        { id: 4, name: '银行卡', amount: 8044, percentage: 6.4, count: 23 }
      ]
    }
  }
}

// 热销商品数据
const productRankData = ref<ProductRankingData>({ period: 'today', products: [] })

const loadProductRankData = async () => {
  try {
    const data = await getProductRanking(productRankTimeRange.value)
    productRankData.value = data
  } catch (error) {
    console.error('加载商品排行数据失败:', error)
    // 使用默认数据
    const timeRanges = {
      today: [
        { name: '招牌奶茶', sales: 156, revenue: 4680 },
        { name: '珍珠奶茶', sales: 134, revenue: 4020 },
        { name: '布丁奶茶', sales: 98, revenue: 2940 },
        { name: '红豆奶茶', sales: 87, revenue: 2610 },
        { name: '椰果奶茶', sales: 76, revenue: 2280 }
      ],
      week: [
        { name: '招牌奶茶', sales: 1056, revenue: 31680 },
        { name: '珍珠奶茶', sales: 934, revenue: 28020 },
        { name: '布丁奶茶', sales: 698, revenue: 20940 },
        { name: '红豆奶茶', sales: 587, revenue: 17610 },
        { name: '椰果奶茶', sales: 476, revenue: 14280 }
      ],
      month: [
        { name: '招牌奶茶', sales: 4556, revenue: 136680 },
        { name: '珍珠奶茶', sales: 3934, revenue: 118020 },
        { name: '布丁奶茶', sales: 2998, revenue: 89940 },
        { name: '红豆奶茶', sales: 2587, revenue: 77610 },
        { name: '椰果奶茶', sales: 2176, revenue: 65280 }
      ],
      year: [
        { name: '招牌奶茶', sales: 54556, revenue: 1636680 },
        { name: '珍珠奶茶', sales: 47934, revenue: 1438020 },
        { name: '布丁奶茶', sales: 35998, revenue: 1079940 },
        { name: '红豆奶茶', sales: 31587, revenue: 947610 },
        { name: '椰果奶茶', sales: 26176, revenue: 785280 }
      ]
    }
    productRankData.value = {
      period: productRankTimeRange.value,
      products: timeRanges[productRankTimeRange.value as keyof typeof timeRanges]
    }
  }
}

// 初始化营业额趋势图
const initRevenueChart = () => {
  if (!revenueChartRef.value) return
  
  revenueChart = echarts.init(revenueChartRef.value)
  updateRevenueChart()
}

// 更新营业额趋势图
const updateRevenueChart = () => {
  if (!revenueChart) return
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#606266'
      },
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>营业额: ¥${point.value.toLocaleString()}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: revenueData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toString()
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      }
    },
    series: [{
      name: '营业额',
      type: 'line',
      smooth: true,
      data: revenueData.value.data,
      lineStyle: {
        width: 3,
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 1, y2: 0,
          colorStops: [
            { offset: 0, color: '#415A77' },
            { offset: 1, color: '#778DA9' }
          ]
        }
      },
      itemStyle: {
        color: '#415A77',
        borderWidth: 2,
        borderColor: '#ffffff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(65, 90, 119, 0.3)' },
            { offset: 1, color: 'rgba(65, 90, 119, 0.05)' }
          ]
        }
      },
      symbol: 'circle',
      symbolSize: 6
    }]
  }
  
  revenueChart.setOption(option)
}

// 初始化门店对比图
const initShopCompareChart = () => {
  if (!shopCompareChartRef.value) return

  shopCompareChart = echarts.init(shopCompareChartRef.value)
  updateShopCompareChart()
}

// 更新门店对比图
const updateShopCompareChart = () => {
  if (!shopCompareChart) return



  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>营业额: ¥${point.value.toLocaleString()}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: shopCompareData.value.shopNames,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toString()
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      }
    },
    series: [{
      name: '营业额',
      type: 'bar',
      data: shopCompareData.value.revenues,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#415A77' },
            { offset: 1, color: '#778DA9' }
          ]
        },
        borderRadius: [4, 4, 0, 0]
      },
      barWidth: '60%'
    }]
  }

  shopCompareChart.setOption(option)
}

// 初始化支付方式饼图
const initPaymentChart = () => {
  if (!paymentChartRef.value) return

  paymentChart = echarts.init(paymentChartRef.value)
  updatePaymentChart()
}

// 更新支付方式图表
const updatePaymentChart = () => {
  if (!paymentChart) return


  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      formatter: '{a} <br/>{b}: {c}% ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#606266',
        fontSize: 12
      }
    },
    series: [{
      name: '支付方式',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['40%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 4,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold',
          color: '#606266'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        ...paymentData.value.payment_methods.map((method, index) => ({
          value: method.percentage,
          name: method.name,
          itemStyle: {
            color: ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C', '#909399'][index % 5]
          }
        }))
      ]
    }]
  }
  
  paymentChart.setOption(option)
}

// 初始化商品排行图
const initProductRankChart = () => {
  if (!productRankChartRef.value) return

  productRankChart = echarts.init(productRankChartRef.value)
  updateProductRankChart()
}

// 更新商品排行图表
const updateProductRankChart = () => {
  if (!productRankChart) return


  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const point = params[0]
        const product = productRankData.value.products.find(p => p.name === point.name)
        return `${point.name}<br/>销量: ${point.value}<br/>营业额: ¥${product?.revenue}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: productRankData.value.products.map(product => product.name),
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#909399',
        fontSize: 12
      }
    },
    series: [{
      name: '销量',
      type: 'bar',
      data: productRankData.value.products.map(product => product.sales),
      itemStyle: {
        color: (params: any) => {
          const colors = ['#E8B86D', '#B8C5D1', '#C7D2DD', '#778DA9', '#415A77']
          return colors[params.dataIndex] || '#415A77'
        },
        borderRadius: [0, 4, 4, 0]
      },
      barWidth: '50%'
    }]
  }
  
  productRankChart.setOption(option)
}

// 初始化所有图表
const initAllCharts = async () => {
  await nextTick()
  initRevenueChart()
  initShopCompareChart()
  initPaymentChart()
  initProductRankChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    revenueChart?.resize()
    shopCompareChart?.resize()
    paymentChart?.resize()
    productRankChart?.resize()
  })
}

// 初始化所有数据
const initAllData = async () => {
  loading.value = true
  try {
    // 并行加载所有数据
    await Promise.all([
      loadOverviewData(),
      loadRevenueData(),
      loadShopCompareData(),
      loadPaymentData(),
      loadProductRankData(),
      loadShopStatusData()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  console.log('数据大屏页面已加载')
  await initAllData()
  initAllCharts()
})
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --glass-background: rgba(255, 255, 255, 0.92);
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
}

/* 页面内容样式 */
.page-container {
  padding: 12px;
  min-height: 100%;
  overflow-y: visible;
  background: #f8f9fa;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--text-light);
  font-size: 14px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-icon.shops {
  background: linear-gradient(135deg, #415A77 0%, #778DA9 100%);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
}

.stat-icon.orders {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 100%);
}

.stat-icon.members {
  background: linear-gradient(135deg, #722ED1 0%, #9254DE 100%);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.1;
  margin: 0;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
}



/* 图表分析区域 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 16px var(--shadow-light);
  border: 1px solid rgba(65, 90, 119, 0.08);
  overflow: hidden;
  /* 减小圆角以保持一致性 */
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(65, 90, 119, 0.1);
  background: rgba(65, 90, 119, 0.02);
  /* 减少内边距 */
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.time-selector {
  display: flex;
  gap: 4px;
}

.time-btn {
  padding: 6px 12px;
  border: 1px solid var(--accent-platinum);
  background: white;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.time-btn.active {
  background: var(--primary-steel-blue);
  color: white;
  border-color: var(--primary-steel-blue);
}

.time-btn:hover:not(.active) {
  border-color: var(--primary-steel-blue);
  color: var(--primary-steel-blue);
}

.chart-body {
  padding: 20px;
  height: 280px;
  /* 减少内边距和高度，提高空间利用率 */
}

.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 表格卡片 */
.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
  border: 1px solid #e4e7ed;
  overflow: hidden;
  margin-bottom: 20px;
  /* 减少底部间距 */
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafbfc;
  /* 减少内边距 */
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.refresh-btn {
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  background: white;
  color: var(--text-secondary);
  font-size: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  border-color: var(--primary-steel-blue);
  color: var(--primary-steel-blue);
}

.table-body {
  /* 移除固定高度和滚动条，让表格自然展开 */
}

/* 表格样式 */
.table-container {
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.table th {
  background: var(--secondary-light-blue-gray);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 1;
}

.table td {
  color: var(--text-secondary);
  font-size: 13px;
}

.table tr:hover {
  background: rgba(65, 90, 119, 0.05);
}

.table tr.row-offline {
  opacity: 0.6;
}

/* 门店名称样式 */
.shop-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.shop-name .name {
  font-weight: 500;
}

.shop-name .rank {
  background: var(--warning-color);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

/* 状态标识增强 */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-tag.online {
  background: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.status-tag.offline {
  background: rgba(245, 108, 108, 0.1);
  color: var(--danger-color);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* 营业额单元格 */
.revenue-cell {
  min-width: 120px;
}

.revenue-cell .amount {
  font-weight: 600;
  color: var(--text-primary);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #f0f2f5;
  border-radius: 2px;
  margin-top: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--primary-steel-blue));
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 利润单元格 */
.profit-cell .profit-amount {
  font-weight: 500;
}

.profit-cell .profit-rate {
  color: var(--text-muted);
  font-size: 11px;
  display: block;
  margin-top: 2px;
}

/* 库存状态 */
.stock-status {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.stock-status.normal {
  background: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
}

.stock-status.warning {
  background: rgba(230, 162, 60, 0.1);
  color: var(--warning-color);
}

.stock-status.low {
  background: rgba(245, 108, 108, 0.1);
  color: var(--danger-color);
}

/* 更新时间 */
.update-time {
  color: var(--text-muted);
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .time-selector {
    flex-wrap: wrap;
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
}
</style>