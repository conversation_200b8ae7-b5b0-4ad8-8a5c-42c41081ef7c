<?php

namespace app\company\controller;

use app\model\CsShop;
use app\model\CsSystemConfig;
use app\service\SystemConfigService;
use support\Request;
use support\Response;
use support\Log;
use support\Db;

class CsConfigController
{
    /**
     * 获取全局配置
     * @param Request $request
     * @return Response
     */
    public function getGlobalConfig(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $category = $request->get('category', ''); // 配置分类，空表示所有
            
            $configData = $this->getCompanyConfig($companyId, $category);
            
            return success([
                'category' => $category,
                'config' => $configData
            ]);
        } catch (\Exception $e) {
            Log::error('获取全局配置失败: ' . $e->getMessage());
            return fail('获取配置失败');
        }
    }

    /**
     * 更新全局配置
     * @param Request $request
     * @return Response
     */
    public function updateGlobalConfig(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $configs = $request->post('configs', []); // 配置项数组
            
            if (empty($configs) || !is_array($configs)) {
                return fail('配置参数错误');
            }
            
            Db::beginTransaction();
            
            foreach ($configs as $configKey => $configValue) {
                $this->updateSingleConfig($companyId, $configKey, $configValue);
            }
            
            Db::commit();
            
            Log::info("公司全局配置更新成功，公司ID: {$companyId}");
            return success('配置更新成功');
        } catch (\Exception $e) {
            Db::rollBack();
            Log::error('更新全局配置失败: ' . $e->getMessage());
            return fail('更新配置失败');
        }
    }

    /**
     * 同步配置到门店
     * @param Request $request
     * @return Response
     */
    public function syncToShops(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $configKeys = $request->post('config_keys', []); // 要同步的配置键
            $shopIds = $request->post('shop_ids', []); // 目标门店ID，空表示所有门店
            
            if (empty($configKeys) || !is_array($configKeys)) {
                return fail('请选择要同步的配置项');
            }
            
            // 获取目标门店
            $shops = CsShop::where('company_id', $companyId);
            if (!empty($shopIds) && is_array($shopIds)) {
                $shops = $shops->whereIn('id', $shopIds);
            }
            $shops = $shops->get();
            
            if ($shops->isEmpty()) {
                return fail('没有找到目标门店');
            }
            
            Db::beginTransaction();
            
            $syncResults = [];
            foreach ($shops as $shop) {
                $result = $this->syncConfigToShop($companyId, $shop->id, $configKeys);
                $syncResults[] = [
                    'shop_id' => $shop->id,
                    'shop_name' => $shop->shop_name,
                    'success' => $result['success'],
                    'synced_count' => $result['count']
                ];
            }
            
            Db::commit();
            
            Log::info("配置同步完成，公司ID: {$companyId}");
            return success([
                'message' => '配置同步完成',
                'sync_results' => $syncResults
            ]);
        } catch (\Exception $e) {
            Db::rollBack();
            Log::error('同步配置到门店失败: ' . $e->getMessage());
            return fail('配置同步失败');
        }
    }

    /**
     * 获取配置分类列表
     * @param Request $request
     * @return Response
     */
    public function getConfigCategories(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            
            $categories = CsSystemConfig::where('company_id', $companyId)
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category')
                ->filter()
                ->values();
            
            return success([
                'categories' => $categories
            ]);
        } catch (\Exception $e) {
            Log::error('获取配置分类失败: ' . $e->getMessage());
            return fail('获取配置分类失败');
        }
    }

    /**
     * 重置配置到默认值
     * @param Request $request
     * @return Response
     */
    public function resetToDefault(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $configKeys = $request->post('config_keys', []);
            
            if (empty($configKeys) || !is_array($configKeys)) {
                return fail('请选择要重置的配置项');
            }
            
            Db::beginTransaction();
            
            $resetCount = 0;
            foreach ($configKeys as $configKey) {
                $defaultValue = $this->getDefaultConfigValue($configKey);
                if ($defaultValue !== null) {
                    $this->updateSingleConfig($companyId, $configKey, $defaultValue);
                    $resetCount++;
                }
            }
            
            Db::commit();
            
            Log::info("配置重置完成，公司ID: {$companyId}, 重置数量: {$resetCount}");
            return success([
                'message' => '配置重置成功',
                'reset_count' => $resetCount
            ]);
        } catch (\Exception $e) {
            Db::rollBack();
            Log::error('重置配置失败: ' . $e->getMessage());
            return fail('配置重置失败');
        }
    }

    /**
     * 批量配置管理
     * @param Request $request
     * @return Response
     */
    public function batchManage(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $action = $request->post('action', ''); // create, update, delete
            $configItems = $request->post('config_items', []);
            
            if (empty($action) || empty($configItems) || !is_array($configItems)) {
                return fail('参数错误');
            }
            
            Db::beginTransaction();
            
            $results = [];
            switch ($action) {
                case 'create':
                    $results = $this->batchCreateConfigs($companyId, $configItems);
                    break;
                case 'update':
                    $results = $this->batchUpdateConfigs($companyId, $configItems);
                    break;
                case 'delete':
                    $results = $this->batchDeleteConfigs($companyId, $configItems);
                    break;
                default:
                    throw new \Exception('不支持的操作类型');
            }
            
            Db::commit();
            
            Log::info("批量配置操作完成，公司ID: {$companyId}, 操作: {$action}");
            return success([
                'message' => '批量操作完成',
                'action' => $action,
                'results' => $results
            ]);
        } catch (\Exception $e) {
            Db::rollBack();
            Log::error('批量配置管理失败: ' . $e->getMessage());
            return fail('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取公司配置
     * @param int $companyId
     * @param string $category
     * @return array
     */
    private function getCompanyConfig(int $companyId, string $category = ''): array
    {
        try {
            $query = CsSystemConfig::where('company_id', $companyId);
            
            if (!empty($category)) {
                $query->where('category', $category);
            }
            
            $configs = $query->get()->map(function ($config) {
                return [
                    'key' => $config->config_key,
                    'value' => $config->config_value,
                    'category' => $config->category,
                    'description' => $config->description,
                    'type' => $config->value_type,
                    'updated_at' => $config->updated_at
                ];
            });
            
            return $configs->groupBy('category')->toArray();
        } catch (\Exception $e) {
            Log::error('获取公司配置失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 更新单个配置项
     * @param int $companyId
     * @param string $configKey
     * @param mixed $configValue
     * @return bool
     */
    private function updateSingleConfig(int $companyId, string $configKey, $configValue): bool
    {
        try {
            $config = CsSystemConfig::where('company_id', $companyId)
                ->where('config_key', $configKey)
                ->first();
            
            if (!$config) {
                // 创建新配置项
                $config = new CsSystemConfig();
                $config->company_id = $companyId;
                $config->config_key = $configKey;
                $config->category = $this->getConfigCategory($configKey);
                $config->value_type = $this->getConfigType($configValue);
            }
            
            $config->config_value = is_array($configValue) ? json_encode($configValue) : $configValue;
            $config->updated_at = date('Y-m-d H:i:s');
            
            return $config->save();
        } catch (\Exception $e) {
            Log::error('更新单个配置失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 同步配置到单个门店
     * @param int $companyId
     * @param int $shopId
     * @param array $configKeys
     * @return array
     */
    private function syncConfigToShop(int $companyId, int $shopId, array $configKeys): array
    {
        try {
            $syncCount = 0;
            
            foreach ($configKeys as $configKey) {
                $companyConfig = CsSystemConfig::where('company_id', $companyId)
                    ->where('config_key', $configKey)
                    ->first();
                
                if ($companyConfig) {
                    $shopConfig = CsSystemConfig::where('shop_id', $shopId)
                        ->where('config_key', $configKey)
                        ->first();
                    
                    if (!$shopConfig) {
                        $shopConfig = new CsSystemConfig();
                        $shopConfig->shop_id = $shopId;
                        $shopConfig->config_key = $configKey;
                        $shopConfig->category = $companyConfig->category;
                        $shopConfig->value_type = $companyConfig->value_type;
                    }
                    
                    $shopConfig->config_value = $companyConfig->config_value;
                    $shopConfig->description = $companyConfig->description;
                    $shopConfig->updated_at = date('Y-m-d H:i:s');
                    
                    if ($shopConfig->save()) {
                        $syncCount++;
                    }
                }
            }
            
            return ['success' => true, 'count' => $syncCount];
        } catch (\Exception $e) {
            Log::error('同步配置到门店失败: ' . $e->getMessage());
            return ['success' => false, 'count' => 0];
        }
    }

    /**
     * 获取配置项的默认值
     * @param string $configKey
     * @return mixed|null
     */
    private function getDefaultConfigValue(string $configKey)
    {
        $defaultConfigs = [
            'shop_name' => '',
            'shop_address' => '',
            'shop_phone' => '',
            'business_hours' => '09:00-22:00',
            'table_auto_clean' => '1',
            'order_timeout' => '30',
            'print_auto' => '0',
            'member_discount' => '0.95',
            'point_rate' => '100',
            // 添加更多默认配置...
        ];
        
        return $defaultConfigs[$configKey] ?? null;
    }

    /**
     * 获取配置项的分类
     * @param string $configKey
     * @return string
     */
    private function getConfigCategory(string $configKey): string
    {
        $categoryMap = [
            'shop_name' => 'basic',
            'shop_address' => 'basic',
            'shop_phone' => 'basic',
            'business_hours' => 'business',
            'table_auto_clean' => 'business',
            'order_timeout' => 'business',
            'print_auto' => 'print',
            'member_discount' => 'member',
            'point_rate' => 'member',
            // 添加更多分类映射...
        ];
        
        return $categoryMap[$configKey] ?? 'other';
    }

    /**
     * 获取配置值的类型
     * @param mixed $value
     * @return string
     */
    private function getConfigType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    /**
     * 批量创建配置
     * @param int $companyId
     * @param array $configItems
     * @return array
     */
    private function batchCreateConfigs(int $companyId, array $configItems): array
    {
        $results = [];
        
        foreach ($configItems as $item) {
            if (empty($item['key'])) {
                $results[] = ['key' => '', 'success' => false, 'message' => '配置键不能为空'];
                continue;
            }
            
            $existing = CsSystemConfig::where('company_id', $companyId)
                ->where('config_key', $item['key'])
                ->exists();
            
            if ($existing) {
                $results[] = ['key' => $item['key'], 'success' => false, 'message' => '配置已存在'];
                continue;
            }
            
            $config = new CsSystemConfig();
            $config->company_id = $companyId;
            $config->config_key = $item['key'];
            $config->config_value = $item['value'] ?? '';
            $config->category = $item['category'] ?? $this->getConfigCategory($item['key']);
            $config->description = $item['description'] ?? '';
            $config->value_type = $this->getConfigType($item['value'] ?? '');
            
            if ($config->save()) {
                $results[] = ['key' => $item['key'], 'success' => true, 'message' => '创建成功'];
            } else {
                $results[] = ['key' => $item['key'], 'success' => false, 'message' => '创建失败'];
            }
        }
        
        return $results;
    }

    /**
     * 批量更新配置
     * @param int $companyId
     * @param array $configItems
     * @return array
     */
    private function batchUpdateConfigs(int $companyId, array $configItems): array
    {
        $results = [];
        
        foreach ($configItems as $item) {
            if (empty($item['key'])) {
                $results[] = ['key' => '', 'success' => false, 'message' => '配置键不能为空'];
                continue;
            }
            
            $success = $this->updateSingleConfig($companyId, $item['key'], $item['value'] ?? '');
            $results[] = [
                'key' => $item['key'],
                'success' => $success,
                'message' => $success ? '更新成功' : '更新失败'
            ];
        }
        
        return $results;
    }

    /**
     * 批量删除配置
     * @param int $companyId
     * @param array $configItems
     * @return array
     */
    private function batchDeleteConfigs(int $companyId, array $configItems): array
    {
        $results = [];
        
        foreach ($configItems as $item) {
            $configKey = is_array($item) ? $item['key'] : $item;
            
            if (empty($configKey)) {
                $results[] = ['key' => '', 'success' => false, 'message' => '配置键不能为空'];
                continue;
            }
            
            $deleted = CsSystemConfig::where('company_id', $companyId)
                ->where('config_key', $configKey)
                ->delete();
            
            $results[] = [
                'key' => $configKey,
                'success' => $deleted > 0,
                'message' => $deleted > 0 ? '删除成功' : '配置不存在或删除失败'
            ];
        }
        
        return $results;
    }
}