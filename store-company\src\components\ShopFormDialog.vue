<template>
  <div v-if="visible" class="dialog-overlay">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3>{{ isEdit ? '编辑门店' : '新增门店' }}</h3>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      
      <div class="dialog-body">
        <!-- Tab 导航 -->
        <div class="tab-navigation">
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'basic' }" 
            @click="activeTab = 'basic'"
          >
            <span class="tab-icon">📋</span>
            基本信息
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: activeTab === 'hours' }" 
            @click="activeTab = 'hours'"
          >
            <span class="tab-icon">🕐</span>
            营业时间
          </button>
        </div>

        <!-- Tab 内容 -->
        <div class="tab-content" style="min-height: 400px; background: white; display: block;">

          <!-- 基本信息 Tab -->
          <div v-if="activeTab === 'basic'" class="tab-panel" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
              <div class="form-section">
              <h4 class="section-title">门店基本信息</h4>
              <div class="form-row">
                <div class="form-field">
                  <label class="field-label">门店名称 <span class="required">*</span></label>
                  <input 
                    v-model="formData.name" 
                    type="text" 
                    class="form-input" 
                    :class="{ 'error': formErrors.name }"
                    placeholder="请输入门店名称"
                    @input="clearFieldError('name')"
                  >
                  <span v-if="formErrors.name" class="error-message">{{ formErrors.name }}</span>
                </div>
                <div class="form-field">
                  <label class="field-label">门店负责人 <span class="required">*</span></label>
                  <input 
                    v-model="formData.manager" 
                    type="text" 
                    class="form-input"
                    :class="{ 'error': formErrors.manager }"
                    placeholder="请输入负责人姓名"
                    @input="clearFieldError('manager')"
                  >
                  <span v-if="formErrors.manager" class="error-message">{{ formErrors.manager }}</span>
                </div>
                <div class="form-field">
                  <label class="field-label">营业状态</label>
                  <select v-model="formData.status" class="form-select">
                    <option value="active">营业中</option>
                    <option value="pending">待开业</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="section-title">联系方式</h4>
              <div class="form-row">
                <div class="form-field">
                  <label class="field-label">登录手机 <span class="required">*</span></label>
                  <input 
                    v-model="formData.phone" 
                    type="tel" 
                    class="form-input"
                    :class="{ 'error': formErrors.phone }"
                    placeholder="请输入登录手机号"
                    @input="clearFieldError('phone')"
                  >
                  <span v-if="formErrors.phone" class="error-message">{{ formErrors.phone }}</span>
                </div>
                <div class="form-field">
                  <label class="field-label">登录密码</label>
                  <input 
                    v-model="formData.password" 
                    type="text" 
                    class="form-input" 
                    placeholder="请输入登录密码"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">服务电话</label>
                  <input 
                    v-model="formData.servicePhone" 
                    type="tel" 
                    class="form-input" 
                    placeholder="请输入客服电话"
                  >
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="section-title">门店位置</h4>
              <div class="form-row">
                <div class="form-field full-width">
                  <label class="field-label">门店地址 <span class="required">*</span></label>
                  <div class="address-input-group">
                    <input 
                      v-model="formData.address" 
                      type="text" 
                      class="form-input"
                      :class="{ 'error': formErrors.address }"
                      placeholder="请输入详细地址"
                      @input="clearFieldError('address')"
                    >
                    <button class="location-btn" type="button" v-if="false">
                      📍 选择位置
                    </button>
                  </div>
                  <span v-if="formErrors.address" class="error-message">{{ formErrors.address }}</span>
                </div>
              </div>
              <div class="form-row" v-if="false">
                <div class="form-field">
                  <label class="field-label">经度坐标</label>
                  <input 
                    v-model="formData.longitude" 
                    type="text" 
                    class="form-input" 
                    placeholder="longitude"
                  >
                </div>
                <div class="form-field">
                  <label class="field-label">纬度坐标</label>
                  <input 
                    v-model="formData.latitude" 
                    type="text" 
                    class="form-input" 
                    placeholder="latitude"
                  >
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="section-title">备注信息</h4>
              <div class="form-row">
                <div class="form-field full-width">
                  <label class="field-label">备注说明</label>
                  <textarea 
                    v-model="formData.description" 
                    class="form-textarea" 
                    placeholder="请输入备注说明"
                    rows="4"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <!-- 营业时间 Tab -->
          <div v-if="activeTab === 'hours'" class="tab-panel" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
              <div class="form-section">
              <h4 class="section-title">营业时间设置</h4>
              <p class="section-desc">设置门店每天的营业时间，可以单独设置某天为休息日</p>
              
              <div class="business-hours-list">
                <div 
                  v-for="day in weekDays" 
                  :key="day.key" 
                  class="hours-row"
                >
                  <div class="day-label">{{ day.label }}</div>
                  <div class="hours-controls">
                    <span class="status-tag" :class="day.isOpen ? 'active' : 'closed'">
                      {{ day.isOpen ? '营业' : '休息' }}
                    </span>
                    <template v-if="day.isOpen">
                      <input 
                        v-model="day.startTime" 
                        type="time" 
                        class="time-input"
                      >
                      <span class="time-separator">至</span>
                      <input 
                        v-model="day.endTime" 
                        type="time" 
                        class="time-input"
                      >
                    </template>
                    <div class="toggle-switch" :class="{ active: day.isOpen }" @click="toggleDayStatus(day)">
                      <div class="switch-handle" :class="{ active: day.isOpen }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-cancel" @click="handleClose">取消</button>
        <button class="btn btn-primary" @click="handleSave">{{ isEdit ? '保存' : '创建' }}</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getShopDetail, type Shop, type BusinessHours } from '../services/shopApi'

// 表单数据接口
interface FormData {
  name: string
  manager: string
  phone: string
  address: string
  servicePhone: string
  status: 'active' | 'pending'
  password: string
  longitude: string
  latitude: string
  description: string
}

interface WeekDay {
  key: string
  label: string
  isOpen: boolean
  startTime: string
  endTime: string
}

// 定义 props
interface Props {
  visible: boolean
  isEdit: boolean
  editingShop?: Shop | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  editingShop: null
})

// 定义 emits
const emit = defineEmits<{
  close: []
  save: [formData: FormData, weekDays: WeekDay[]]
}>()

// 响应式数据
const activeTab = ref<'basic' | 'hours'>('basic')

const formData = reactive<FormData>({
  name: '',
  manager: '',
  phone: '',
  address: '',
  servicePhone: '',
  status: 'active',
  password: '',
  longitude: '',
  latitude: '',
  description: ''
})

// 表单错误状态
const formErrors = reactive({
  name: '',
  manager: '',
  phone: '',
  address: '',
  servicePhone: '',
  password: '',
  longitude: '',
  latitude: '',
  description: ''
})

const weekDays = reactive<WeekDay[]>([
  { key: 'monday', label: '周一', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'tuesday', label: '周二', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'wednesday', label: '周三', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'thursday', label: '周四', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'friday', label: '周五', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'saturday', label: '周六', isOpen: true, startTime: '09:00', endTime: '22:00' },
  { key: 'sunday', label: '周日', isOpen: true, startTime: '09:00', endTime: '22:00' }
])

// 方法
const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    manager: '',
    phone: '',
    address: '',
    servicePhone: '',
    status: 'active',
    password: '',
    longitude: '',
    latitude: '',
    description: ''
  })
  
  // 清空错误状态
  Object.assign(formErrors, {
    name: '',
    manager: '',
    phone: '',
    address: '',
    servicePhone: '',
    password: '',
    longitude: '',
    latitude: '',
    description: ''
  })
  
  weekDays.forEach(day => {
    day.isOpen = true
    day.startTime = '09:00'
    day.endTime = '22:00'
  })
}

// 清空特定字段的错误
const clearFieldError = (field: keyof typeof formErrors) => {
  formErrors[field] = ''
}

const fillFormData = async (shop: Shop) => {
  // 如果编辑，先获取完整的门店信息
  let shopDetail = shop
  if (shop.id) {
    try {
      const response = await getShopDetail(shop.id)
      shopDetail = response
    } catch (error) {
      console.error('获取门店详情失败:', error)
    }
  }

  Object.assign(formData, {
    name: shopDetail.shop_name,
    manager: shopDetail.nickname,
    phone: shopDetail.mobile,
    address: shopDetail.address,
    servicePhone: shopDetail.service_phone || '',
    status: shopDetail.status === 1 ? 'active' : 'pending',
    password: shopDetail.password || '',
    longitude: shopDetail.longitude?.toString() || '',
    latitude: shopDetail.latitude?.toString() || '',
    description: shopDetail.remark || ''
  })

  // 填充营业时间
  if (shopDetail.business_hours_config) {
    const businessHours = shopDetail.business_hours_config
    const dayMapping = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    weekDays.forEach((day, index) => {
      const dayKey = dayMapping[index]
      const dayData = businessHours[dayKey as keyof BusinessHours]
      if (dayData) {
        day.isOpen = dayData.is_open
        day.startTime = dayData.open
        day.endTime = dayData.close
      }
    })
  }
}

const toggleDayStatus = (day: WeekDay) => {
  day.isOpen = !day.isOpen
}

const handleClose = () => {
  emit('close')
}

// 表单验证规则
const validateForm = (): boolean => {
  let isValid = true
  
  // 清空之前的错误
  Object.assign(formErrors, {
    name: '',
    manager: '',
    phone: '',
    address: '',
    servicePhone: '',
    password: '',
    longitude: '',
    latitude: '',
    description: ''
  })

  // 去除所有字段的前后空格
  formData.name = formData.name.trim()
  formData.manager = formData.manager.trim()
  formData.phone = formData.phone.trim()
  formData.address = formData.address.trim()
  formData.servicePhone = formData.servicePhone.trim()
  formData.password = formData.password.trim()
  formData.longitude = formData.longitude.trim()
  formData.latitude = formData.latitude.trim()
  formData.description = formData.description.trim()

  // 必填字段验证
  if (!formData.name) {
    formErrors.name = '请输入门店名称'
    isValid = false
  } else if (formData.name.length < 2 || formData.name.length > 50) {
    formErrors.name = '门店名称长度应在2-50个字符之间'
    isValid = false
  }

  if (!formData.manager) {
    formErrors.manager = '请输入门店负责人'
    isValid = false
  } else if (formData.manager.length < 2 || formData.manager.length > 20) {
    formErrors.manager = '负责人姓名长度应在2-20个字符之间'
    isValid = false
  }

  if (!formData.phone) {
    formErrors.phone = '请输入登录手机号'
    isValid = false
  } else {
    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.phone)) {
      formErrors.phone = '请输入正确的手机号格式'
      isValid = false
    }
  }

  if (!formData.address) {
    formErrors.address = '请输入门店地址'
    isValid = false
  } else if (formData.address.length < 5 || formData.address.length > 200) {
    formErrors.address = '门店地址长度应在5-200个字符之间'
    isValid = false
  }

  // 可选字段验证
  if (formData.servicePhone && formData.servicePhone.length > 0) {
    const servicePhoneRegex = /^(\d{3,4}-\d{7,8}|\d{11})$/
    if (!servicePhoneRegex.test(formData.servicePhone)) {
      formErrors.servicePhone = '服务电话格式不正确，请输入座机号(如010-12345678)或手机号'
      isValid = false
    }
  }

  if (formData.password && formData.password.length > 0) {
    if (formData.password.length < 6 || formData.password.length > 20) {
      formErrors.password = '登录密码长度应在6-20个字符之间'
      isValid = false
    }
  }

  // 经纬度验证
  if (formData.longitude && formData.longitude.length > 0) {
    const longitude = parseFloat(formData.longitude)
    if (isNaN(longitude) || longitude < -180 || longitude > 180) {
      formErrors.longitude = '经度值应在-180到180之间'
      isValid = false
    }
  }

  if (formData.latitude && formData.latitude.length > 0) {
    const latitude = parseFloat(formData.latitude)
    if (isNaN(latitude) || latitude < -90 || latitude > 90) {
      formErrors.latitude = '纬度值应在-90到90之间'
      isValid = false
    }
  }

  if (formData.description && formData.description.length > 500) {
    formErrors.description = '备注说明不能超过500个字符'
    isValid = false
  }

  // 营业时间验证
  const hasOpenDay = weekDays.some(day => day.isOpen)
  if (!hasOpenDay) {
    ElMessage.error('至少需要设置一天营业时间')
    isValid = false
  }

  // 验证营业时间的开始和结束时间
  for (const day of weekDays) {
    if (day.isOpen) {
      if (!day.startTime || !day.endTime) {
        ElMessage.error(`请设置${day.label}的营业时间`)
        isValid = false
        break
      }

      if (day.startTime >= day.endTime) {
        ElMessage.error(`${day.label}的结束时间必须晚于开始时间`)
        isValid = false
        break
      }
    }
  }

  // 如果有验证错误，显示第一个错误并切换到对应的tab
  if (!isValid) {
    const hasBasicError = formErrors.name || formErrors.manager || formErrors.phone || 
                         formErrors.address || formErrors.servicePhone || formErrors.password || 
                         formErrors.longitude || formErrors.latitude || formErrors.description
    
    if (hasBasicError) {
      activeTab.value = 'basic'
      ElMessage.error('请检查并修正表单中的错误信息')
    } else {
      activeTab.value = 'hours'
    }
  }

  return isValid
}

const handleSave = () => {
  // 执行表单验证
  if (!validateForm()) {
    return
  }

  emit('save', formData, weekDays)
}

// 监听 props 变化
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    activeTab.value = 'basic'
    if (props.isEdit && props.editingShop) {
      await fillFormData(props.editingShop)
    } else {
      resetFormData()
    }
  }
})
</script>

<style scoped>
/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

.dialog-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #909399;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f5f7fa;
  color: #606266;
}

.dialog-body {
  max-height: 70vh;
  overflow-y: auto;
  min-height: 400px;
  background: #fff;
}

/* Tab 导航 */
.tab-navigation {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

.tab-btn {
  /* flex: 1; */
  padding: 16px 20px;
  border: none;
  background: none;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.tab-btn:hover {
  color: #415A77;
  background: rgba(65, 90, 119, 0.05);
}

.tab-btn.active {
  color: #415A77;
  background: white;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #415A77;
  border-radius: 3px 3px 0 0;
}

.tab-icon {
  font-size: 16px;
}

/* Tab 内容 */
.tab-content {
  padding: 24px;
  min-height: 400px;
  background: white;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 表单样式 */
/* .form-section {
  margin-bottom: 32px;
} */

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 4px solid #415A77;
}

.section-desc {
  font-size: 14px;
  color: #909399;
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.required {
  color: #f56c6c;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #415A77;
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #f56c6c;
  box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.1);
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.address-input-group {
  display: flex;
  gap: 8px;
}

.address-input-group .form-input {
  flex: 1;
}

.location-btn {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.location-btn:hover {
  background: #ebeef5;
  color: #415A77;
}

/* 营业时间设置 */
.hours-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  margin-bottom: 12px;
}

.day-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  min-width: 60px;
}

.hours-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-tag.active {
  background: #f0f9ff;
  color: #67C23A;
  border: 1px solid #b3e19d;
}

.status-tag.closed {
  background: #f5f7fa;
  color: #909399;
  border: 1px solid #dcdfe6;
}

.time-input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.time-input:focus {
  outline: none;
  border-color: #415A77;
}

.time-separator {
  font-size: 14px;
  color: #909399;
}

/* 切换开关 */
.toggle-switch {
  width: 48px;
  height: 24px;
  background: #dcdfe6;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-switch:hover {
  background: #c0c4cc;
}

.switch-handle {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.switch-handle.active {
  transform: translateX(24px);
}

.toggle-switch.active {
  background: #67C23A;
}

/* 对话框底部 */
.dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
  background: #fafbfc;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
}

.btn-cancel {
  background: white;
  color: #606266;
  border-color: #dcdfe6;
}

.btn-cancel:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
}

.btn-primary {
  background: #415A77;
  color: white;
  border-color: #415A77;
}

.btn-primary:hover {
  background: #1B365D;
  border-color: #1B365D;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-container {
    width: 95%;
    max-height: 95vh;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .hours-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .hours-controls {
    justify-content: space-between;
  }
}
</style>
